{"name": "bqd-precision-tester", "private": true, "version": "1.2.0", "type": "module", "description": "Bank of Qingdao Precision Tester", "author": {"name": "", "email": ""}, "repository": {"type": "git", "url": ""}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vue-tsc && vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@antv/g6": "^4.8.24", "@element-plus/icons-vue": "^2.3.1", "@icon-park/vue-next": "^1.4.2", "@vueuse/core": "^10.9.0", "axios": "^1.7.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.9.4", "js-base64": "^3.7.7", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.2", "screenfull": "^6.0.2", "vkbeautify": "^0.99.3", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@types/nprogress": "^0.2.3", "@types/qs": "^6.9.12", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.22.0", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "~1.77.2", "stylelint": "^16.1.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.0.0", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.0.0", "typescript": "~5.4.5", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.4.6", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.17.4", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.0.29"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}