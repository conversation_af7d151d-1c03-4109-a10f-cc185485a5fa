<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>文件上传管理</span>
          <div class="header-actions">
            <el-button
              type="success"
              :icon="Tools"
              @click="navigateToDifyCaseGen"
              class="dify-button"
            >
              智能案例生成
            </el-button>
          </div>
        </div>
      </template>

      <!-- 文件上传区域 -->
      <el-upload
        class="upload-demo"
        drag
        multiple
        :action="api_upload()"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :on-progress="handleUploadProgress"
        :show-file-list="false"
        accept=".docx"
        :headers="uploadHeaders"
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">仅支持上传Word文档（.docx）</div>
        </template>
      </el-upload>

      <!-- 上传进度条 -->
      <div v-if="uploadingFiles.length > 0" class="upload-progress-container">
        <div v-for="(file, index) in uploadingFiles" :key="index" class="upload-progress-item">
          <div class="file-info">
            <el-icon class="file-icon">
              <Document />
            </el-icon>
            <span class="file-name">{{ file.name }}</span>
            <span class="progress-text">{{ Math.floor(file.percentage) }}%</span>
          </div>
          <el-progress :percentage="file.percentage" :status="file.status" />
        </div>
      </div>

      <!-- 文件列表展示 -->
      <div class="file-list-container">
        <div class="file-list-header">
          <h3>已上传文件列表</h3>
          <el-button
            type="primary"
            :icon="Refresh"
            circle
            size="small"
            @click="loadFileList"
            title="刷新列表"
            :loading="loading"
          />
        </div>
        <el-table :data="fileList" style="width: 100%" stripe border v-loading="loading">
          <el-table-column prop="name" label="文件名" width="200">
            <template #default="{ row }">
              <div class="file-name-cell">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="file-name">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="上传时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="isProcessing" label="状态">
            <template #default="{ row }">
              <el-tag v-if="row.isProcessing === 0" type="success">处理成功</el-tag>
              <el-tag v-else-if="row.isProcessing === -1" type="danger">异常</el-tag>
              <el-tag v-else type="warning">处理中</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="handleCustomAction(row)">详情</el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)"> 删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页控件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { UploadFilled, Document, Refresh, Tools } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { api_deleteFileById, api_getFileList, api_upload, DemandDoc } from "@/api/modules/service-llm/demand-doc.js";
import { formatTime } from "@/tools/DateTool";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/modules/user";
import { ResultData } from "@/api/interface";

const router = useRouter();
const userStore = useUserStore();
const fileList = ref<DemandDoc[]>([]);
const uploadingFiles = ref<
  Array<{ name: string; percentage: number; status: "" | "success" | "exception" | "warning" | undefined }>
>([]);
const loading = ref(false);

// 上传请求头，添加JWT token认证
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${userStore.getToken}`
  };
});

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadFileList();
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置为第一页
  loadFileList();
};

// 初始化加载文件列表
onMounted(() => {
  loadFileList();
});

// 加载文件列表
const loadFileList = () => {
  loading.value = true;
  api_getFileList(currentPage.value, pageSize.value)
    .then(res => {
      if (res.respCode === 2000) {
        fileList.value = res.respData.pageData;
        currentPage.value = res.respData.pageNo;
        pageSize.value = res.respData.pageSize;
        total.value = res.respData.totalCount;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 上传前的校验
const beforeUpload = file => {
  const isDocx = file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  if (!isDocx) {
    ElMessage.error("只能上传Word文档（.docx）格式!");
    return false;
  }

  // 添加文件到上传列表中
  uploadingFiles.value.push({
    name: file.name,
    percentage: 0,
    status: "exception"
  });

  return true;
};

// 处理上传进度
const handleUploadProgress = (event, file) => {
  const fileIndex = uploadingFiles.value.findIndex(item => item.name === file.name);
  uploadingFiles.value[fileIndex].percentage = event.percent;
  uploadingFiles.value[fileIndex].status = undefined; // 正在上传时没有特殊状态
};

// 上传成功处理
const handleUploadSuccess = (res: ResultData, file) => {
  if (res.respCode === 2000) {
    // 更新上传状态为成功
    const fileIndex = uploadingFiles.value.findIndex(item => item.name === file.name);
    uploadingFiles.value[fileIndex].percentage = 100;
    uploadingFiles.value[fileIndex].status = "success";
    ElMessage.success(`${file.name} 上传成功!`);
    // 上传成功移除
    uploadingFiles.value = uploadingFiles.value.filter(item => item.name !== file.name);
    loadFileList();
  } else {
    ElMessage.error(`${file.name} 上传失败: ${res.respMsg}`);
  }
};

// 上传失败处理
const handleUploadError = (error, file) => {
  console.error("上传失败:", error);
  ElMessage.error(`${file.name} 上传失败!`);

  // 更新上传状态为失败
  const fileIndex = uploadingFiles.value.findIndex(item => item.name === file.name);
  uploadingFiles.value[fileIndex].status = "exception";

  // 延时移除该文件的进度条显示
  setTimeout(() => {
    uploadingFiles.value = uploadingFiles.value.filter(item => item.name !== file.name);
  }, 5000);
};

// 删除文件
const handleDelete = (row: DemandDoc) => {
  ElMessageBox.confirm(`确定要删除文件 "${row.name}" 吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;
    api_deleteFileById(row.id)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success(`文件 "${row.name}" 已删除!`);
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        loadFileList();
      });
  });
};

// 自定义操作按钮功能
const handleCustomAction = row => {
  router.push(`/llm/testHelper/demandList/${row.id}`);
};

// 导航到 Dify 案例生成页面
const navigateToDifyCaseGen = () => {
  router.push('/llm/testHelper/difyCaseGenMd');
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.dify-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
}

.dify-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
}

.upload-demo {
  margin-bottom: 30px;
}

.file-list-container {
  margin-top: 30px;
}

.file-list-container h3 {
  margin-bottom: 15px;
  font-size: 16px;
  color: #606266;
  margin-right: 10px;
}

.file-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.file-name-cell {
  display: flex;
  align-items: center;
}

.file-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-progress-container {
  margin: 20px 0;
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.upload-progress-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
  
  :deep(.el-progress) {
    margin-bottom: 0;
  }
  
  :deep(.el-progress-bar) {
    padding-right: 0;
  }
  
  /* 隐藏Element Plus进度条组件自带的百分比文字 */
  :deep(.el-progress__text) {
    display: none !important;
  }
  
  :deep(.el-progress-bar__outer) {
    height: 6px !important;
    background-color: #f0f2f5;
    border-radius: 3px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  :deep(.el-progress-bar__inner) {
    border-radius: 3px;
    background: linear-gradient(90deg, #409eff 0%, #66b3ff 100%);
    transition: width 0.3s ease;
    position: relative;
    
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
      animation: progress-shine 2s infinite;
    }
  }
  
  :deep(.el-progress--success .el-progress-bar__inner) {
    background: linear-gradient(90deg, #67c23a 0%, #85ce61 100%);
  }
  
  :deep(.el-progress--exception .el-progress-bar__inner) {
    background: linear-gradient(90deg, #f56c6c 0%, #f78989 100%);
  }
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 2px 0;
  
  .file-icon {
    margin-right: 10px;
    font-size: 16px;
    color: #409eff;
    flex-shrink: 0;
  }
  
  .file-name {
    flex: 1;
    font-size: 14px;
    color: #303133;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 12px;
  }
}

.progress-text {
  margin-left: auto;
  font-size: 13px;
  color: #606266;
  font-weight: 600;
  background: #f0f2f5;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 40px;
  text-align: center;
  flex-shrink: 0;
}

/* 进度条光泽动画 */
@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>
