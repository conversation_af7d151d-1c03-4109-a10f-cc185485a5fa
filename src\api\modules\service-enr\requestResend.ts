import http from "@/api";
import { SERVICE_ENR } from "@/api/config/servicePort";
import { DbConnectionInfo } from "@/api/modules/service-integrated-tester/db-management";
import { Page } from "@/api/interface";

export interface EnrRequestResendDb {
  connectionInfoId: string;
}

export interface RequestResendDto {
  taskName?: string;
  sourceDbId: string;
  startTime: string;
  endTime: string;
  targetUrl: string;
  threadNum: number;
  sendTimeout: number;
}

export interface EnrResendTask {
  id?: string;
  taskName?: string;
  sourceDbId?: string;
  targetUrl?: string;
  startTime?: string | number;
  endTime?: string | number;
  resendStatus?: string;
  creatorId?: string;
  createTime?: string | number;
}

export interface EnrResendRecord {
  resendId: string;
  taskId: string;
  interfaceId: string;
}

export const api_addDb = (connectionInfoId: string) => {
  return http.get(SERVICE_ENR + "/requestResend/addDb", { connectionInfoId });
}

export const api_listServer = () => {
  return http.get<DbConnectionInfo[]>(SERVICE_ENR + "/requestResend/listServer");
}

export const api_executeResend = (requestResendDto: RequestResendDto) => {
  return http.post(SERVICE_ENR + "/requestResend/execute", requestResendDto);
}

export const api_executingList = () => {
  return http.get<EnrResendTask[]>(SERVICE_ENR + "/requestResend/executingList");
}

export const api_resendOverview = (enrResendRecord: EnrResendRecord, pageNo: number, pageSize: number) => {
  return http.post<Page<EnrResendRecord[]>>(`${SERVICE_ENR}/requestResend/resendOverview?pageNo=${pageNo}&pageSize=${pageSize}`, enrResendRecord);
}
