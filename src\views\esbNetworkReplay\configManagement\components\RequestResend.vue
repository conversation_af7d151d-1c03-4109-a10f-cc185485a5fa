<template>
  <div class="request-resend">
    <el-form :model="resendForm" label-width="120px" :rules="rules" ref="resendFormRef">
      <el-card class="source-config">
        <template #header>
          <div class="card-header">
            <span>数据源配置</span>
            <el-button type="primary" size="small" @click="openAddServerDrawer">新增数据源</el-button>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="ESB数据源" prop="dataSource">
              <el-select v-model="resendForm.sourceDbId" placeholder="请选择ESB数据源" style="width: 100%">
                <el-option v-for="item in dataSourceOptions" :key="item.id" :label="item.dbName" :value="item.id!" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口ID" prop="interfaceId">
              <el-select v-model="resendForm.interfaceId" placeholder="请选择接口ID" filterable clearable style="width: 100%">
                <el-option v-for="item in interfaceOptions" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="resendForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="resendForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="target-config" style="margin-top: 20px">
        <template #header>
          <div class="card-header">
            <span>目标配置</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目标地址" prop="targetUrl">
              <el-input v-model="resendForm.targetUrl" placeholder="请输入目标地址，例如: http://example.com/api" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超时时间(ms)" prop="timeout">
              <el-input-number v-model="resendForm.sendTimeout" :min="1000" :max="60000" :step="1000" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="线程数量" prop="threadCount">
              <el-input-number v-model="resendForm.threadNum" :min="1" :max="20" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发送间隔(ms)" prop="interval">
              <el-input-number v-model="resendForm.interval" :min="0" :max="10000" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="execute-config" style="margin-top: 20px">
        <template #header>
          <div class="card-header">
            <span>执行配置</span>
          </div>
        </template>

        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="resendForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="resendForm.ignoreError">遇到错误继续执行</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">提交重发任务</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-card>
    </el-form>

    <!-- 任务列表 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>重发任务列表</span>
          <el-button type="primary" size="small" @click="fetchResendTasks">刷新</el-button>
        </div>
      </template>

      <el-table :data="resendTasks" border v-loading="tasksLoading">
        <el-table-column prop="id" label="任务ID" width="120" />
        <el-table-column prop="taskName" label="任务名称" width="180" />
        <el-table-column prop="dataSource" label="数据源" width="120" />
        <el-table-column prop="interfaceId" label="接口ID" width="120" />
        <el-table-column label="时间范围" width="240">
          <template #default="scope">
            {{ formatTimestamp(scope.row.startTime) }} 至 {{ formatTimestamp(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="targetUrl" label="目标地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getTaskStatusType(scope.row.status)">
              {{ getTaskStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="showTaskDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 任务详情弹窗 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="重发任务详情" 
      width="80%"
      :close-on-click-modal="false"
    >
      <resend-task-detail v-if="currentTaskId" :task-id="currentTaskId" />
    </el-dialog>

    <!-- 新增数据源抽屉 -->
    <el-drawer v-model="drawerVisible" title="新增数据源" size="50%" :destroy-on-close="true">
      <div class="drawer-content">
        <el-table :data="dbConnectionList" border v-loading="dbConnectionLoading">
          <el-table-column prop="id" label="ID" width="280" show-overflow-tooltip />
          <el-table-column prop="dbName" label="数据库名称" width="150" />
          <el-table-column prop="dbType" label="数据库类型" width="100" />
          <el-table-column prop="url" label="连接URL" min-width="200" show-overflow-tooltip />
          <el-table-column prop="username" label="用户名" width="100" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="addServer(scope.row.id)" :loading="addingServer === scope.row.id">
                添加
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import ResendTaskDetail from "./ResendTaskDetail.vue";
import { ElMessage, FormInstance } from "element-plus";

import { api_addDb, api_listServer, api_executeResend, api_executingList } from "@/api/modules/service-enr/requestResend";
import type { RequestResendDto, EnrResendTask } from "@/api/modules/service-enr/requestResend";
import { api_getDbConnectionInfo, DbConnectionInfo } from "@/api/modules/service-integrated-tester/db-management";
import { api_fetchInfoColData } from "@/api/modules/service-enr/record";

// 表单引用
const resendFormRef = ref<FormInstance>();

// 表单提交状态
const submitting = ref(false);
const tasksLoading = ref(false);
// const resendingTaskId = ref<string>("");

// 数据源选项
const dataSourceOptions = ref<DbConnectionInfo[]>([]);

// 获取服务器列表
const fetchServerList = () => {
  api_listServer().then(res => {
    if (res.respCode === 2000) {
      dataSourceOptions.value = res.respData;
    } else {
      ElMessage.error(res.respMsg || "获取服务器列表失败");
    }
  });
};

// 接口ID选项
const interfaceOptions = ref<string[]>([]);

// 重发任务列表
const resendTasks = ref<EnrResendTask[]>([]);
const detailDialogVisible = ref(false);
const currentTaskId = ref('');

// 表单数据
interface ResendForm extends Omit<RequestResendDto, 'startTime' | 'endTime'> {
  startTime: string | number;
  endTime: string | number;
  interfaceId?: string;
  interval?: number;
  ignoreError?: boolean;
}

const resendForm = reactive<ResendForm>({
  sourceDbId: "",
  startTime: "",
  endTime: "",
  targetUrl: "",
  threadNum: 5,
  sendTimeout: 30000,
  taskName: "",
  interfaceId: "",
  interval: 0,
  ignoreError: false
});

// 表单验证规则
const rules = {
  sourceDbId: [{ required: true, message: "请选择数据源", trigger: "change" }],
  startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  targetUrl: [
    { required: true, message: "请输入目标地址", trigger: "blur" },
    { pattern: /^https?:\/\/.*$/, message: "请输入有效的URL地址", trigger: "blur" }
  ],
  threadNum: [{ required: true, message: "请设置线程数量", trigger: "change" }],
  sendTimeout: [{ required: true, message: "请设置超时时间", trigger: "change" }],
  taskName: [{ required: true, message: "请输入任务名称", trigger: "blur" }]
};

// 新增数据源抽屉相关
const drawerVisible = ref(false);
const dbConnectionList = ref<DbConnectionInfo[]>([]);
const dbConnectionLoading = ref(false);
const addingServer = ref<string>("");

// 打开新增数据源抽屉
const openAddServerDrawer = () => {
  drawerVisible.value = true;
  fetchDbConnectionList();
};

// 获取数据库连接列表
const fetchDbConnectionList = () => {
  dbConnectionLoading.value = true;
  api_getDbConnectionInfo()
    .then(res => {
      if (res.respCode === 2000) {
        dbConnectionList.value = res.respData;
      } else {
        ElMessage.error(res.respMsg || "获取数据库连接列表失败");
      }
    })
    .finally(() => {
      dbConnectionLoading.value = false;
    });
};

// 添加服务器
const addServer = (connectionInfoId: string) => {
  addingServer.value = connectionInfoId;
  api_addDb(connectionInfoId)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("添加服务器成功");
        drawerVisible.value = false;
      } else {
        ElMessage.error(res.respMsg || "添加服务器失败");
      }
      addingServer.value = "";
    })
    .catch(() => {
      addingServer.value = "";
    });
};

// 提交表单
const submitForm = async () => {
  if (!resendFormRef.value) return;
  
  await resendFormRef.value.validate((valid, fields) => {
    if (valid) {
      submitting.value = true;
      
      // 使用格式化后的日期时间字符串
      const submitData: RequestResendDto = {
        sourceDbId: resendForm.sourceDbId,
        targetUrl: resendForm.targetUrl,
        threadNum: resendForm.threadNum,
        sendTimeout: resendForm.sendTimeout,
        taskName: resendForm.taskName,
        startTime: resendForm.startTime as string,
        endTime: resendForm.endTime as string
      };

      api_executeResend(submitData)
        .then(res => {
          if (res.respCode === 2000) {
            ElMessage.success("重发任务已提交成功");
            resetForm();
            fetchResendTasks();
          } else {
            ElMessage.error(res.respMsg || "提交任务失败");
          }
        })
        .catch(error => {
          console.error('提交任务失败:', error);
          ElMessage.error("提交任务失败，请稍后重试");
        })
        .finally(() => {
          submitting.value = false;
        });
    } else {
      console.log("表单验证失败", fields);
    }
  });
};

// 重置表单
const resetForm = () => {
  if (!resendFormRef.value) return;
  
  Object.assign(resendForm, {
    sourceDbId: "",
    startTime: "",
    endTime: "",
    targetUrl: "",
    threadNum: 5,
    sendTimeout: 30000,
    taskName: "",
    interfaceId: "",
    interval: 0,
    ignoreError: false
  });
  
  // 清除验证状态
  resendFormRef.value.clearValidate();
};

// 获取接口列表
const fetchInterfaceList = () => {
  api_fetchInfoColData(["INTERFACE_ID"]).then(res => {
    if (res.respCode === 2000) {
      interfaceOptions.value = res.respData.map(item => item.interfaceId!).filter(Boolean);
    } else {
      ElMessage.error(res.respMsg || "获取接口列表失败");
    }
  });
};

// 获取任务列表
const fetchResendTasks = () => {
  tasksLoading.value = true;

  api_executingList()
    .then(res => {
      if (res.respCode === 2000) {
        // 转换 EnrResendTask 到前端需要的格式
        resendTasks.value = (res.respData || []).map(task => ({
          ...task,
          dataSource: task.sourceDbId || '',
          status: mapResendStatus(task.resendStatus),
          createTime: task.createTime || ''
        }));
      } else {
        ElMessage.error(res.respMsg || "获取任务列表失败");
      }
    })
    .catch(error => {
      console.error('获取任务列表失败:', error);
      ElMessage.error("获取任务列表失败，请稍后重试");
    })
    .finally(() => {
      tasksLoading.value = false;
    });
};

// 映射后端状态到前端状态
const mapResendStatus = (status?: string) => {
  if (!status) return 'COMPLETED';
  switch (status) {
    case '0': return 'RUNNING';
    case '1': return 'COMPLETED';
    case '-1': return 'FAILED';
    default: return 'COMPLETED';
  }
};

// 获取任务状态样式
const getTaskStatusType = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "primary";
    case "COMPLETED":
      return "success";
    case "FAILED":
      return "danger";
    default:
      return "info";
  }
};

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  switch (status) {
    case "RUNNING":
      return "运行中";
    case "COMPLETED":
      return "已完成";
    case "FAILED":
      return "失败";
    default:
      return "已创建";
  }
};

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return "-";
  const date = new Date(timestamp);
  const padZero = (num: number) => num.toString().padStart(2, '0');
  
  const year = date.getFullYear();
  const month = padZero(date.getMonth() + 1);
  const day = padZero(date.getDate());
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());
  const seconds = padZero(date.getSeconds());
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 显示任务详情
const showTaskDetail = (task: EnrResendTask) => {
  if (task.id) {
    currentTaskId.value = task.id;
    detailDialogVisible.value = true;
  }
};

// 页面加载时获取接口列表、任务列表和服务器列表
onMounted(() => {
  fetchServerList();
  fetchInterfaceList();
  fetchResendTasks();
});
</script>

<style scoped lang="scss">
.request-resend {
  padding: 10px 0;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .drawer-content {
    padding: 20px;
  }
}
</style>
