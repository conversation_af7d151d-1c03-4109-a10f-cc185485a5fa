<template>
  <div class="content-container">
    <!--内容头-->
    <div class="content-header fixed">
      <h3 class="content-title">测试案例-{{ props.selectedNode!.title }}</h3>
      <div class="action-buttons">
        <el-button :disabled="selectedTestCases.length === 0" type="danger" @click="handleBatchDelete" :loading="batchDeleting">
          批量删除({{ selectedTestCases.length }})
        </el-button>
        <el-button type="primary" @click="openGenerateDialog" :loading="generating" :disabled="caseStatus === 1">
          生成测试案例
        </el-button>
        <el-button @click="switchComponent">查看功能点</el-button>
        <el-button type="success" @click="handleCaseReview">用例评审</el-button>
      </div>
    </div>

    <!--内容体-->
    <div class="content-body">
      <!-- 测试案例生成中状态提示 -->
      <div v-if="caseStatus === 1" class="generating-status">
        <el-alert title="测试案例生成中..." type="info" :closable="false" show-icon center>
          <div class="generating-status-content">
            <el-progress :percentage="70" :indeterminate="true" status="warning" />
            <span>系统正在生成测试案例，请稍候...</span>
          </div>
        </el-alert>
      </div>

      <el-table
        v-if="testCaseDetailList.length > 0"
        :data="testCaseDetailList"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="case_id" label="案例ID" min-width="120" />
        <el-table-column prop="case_name" label="案例名称" min-width="180" />
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="test_type" label="测试类型" min-width="100" />
        <el-table-column prop="pre_condition" label="前置条件" min-width="180">
          <template #default="{ row }">
            <div class="pre-condition-cell">{{ row.pre_condition }}</div>
          </template>
        </el-table-column>
        <el-table-column label="测试步骤" min-width="260">
          <template #default="{ row }">
            <div class="test-steps-list no-marker">
              <div v-for="(step, idx) in row.steps" :key="idx">{{ step }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="expected_result" label="预期结果" min-width="180">
          <template #default="{ row }">
            <div class="expected-result-cell">{{ row.expected_result }}</div>
          </template>
        </el-table-column>
        <el-table-column label="锁定状态" width="90">
          <template #default="{ row }">
            <el-tag :type="row.isLocked ? 'success' : 'danger'">
              {{ row.isLocked ? "已锁定" : "未锁定" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="openEditDialog(row)"> 编辑 </el-button>
            <el-button
              :type="row.isLocked === 1 ? 'warning' : 'success'"
              link
              @click="handleToggleLock(row)"
              :loading="lockingCaseId === row.id"
            >
              {{ row.isLocked === 1 ? "解锁" : "锁定" }}
            </el-button>
            <el-button type="danger" link @click="handleSingleDelete(row)" :loading="deletingIds.includes(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else description="暂无测试案例" />
    </div>

    <!-- 生成测试案例弹窗 -->
    <el-dialog v-model="generateDialogVisible" title="生成测试案例" width="50%" destroy-on-close>
      <el-form :model="generateForm" label-position="top">
        <el-form-item label="提示词（可选）">
          <el-input
            v-model="generateForm.prompt"
            type="textarea"
            :rows="4"
            placeholder="请输入提示词，用于指导测试案例生成（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleGenerateTestCase" :loading="generating">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑测试案例弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑测试案例" width="60%" destroy-on-close>
      <el-form :model="editForm" label-position="top" ref="editFormRef">
        <el-form-item label="案例名称" prop="case_name" :rules="[{ required: true, message: '请输入案例名称', trigger: 'blur' }]">
          <el-input v-model="editForm.case_name" placeholder="请输入案例名称" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority" :rules="[{ required: true, message: '请选择优先级', trigger: 'change' }]">
          <el-select v-model="editForm.priority" placeholder="请选择优先级">
            <el-option label="高" value="高" />
            <el-option label="中" value="中" />
            <el-option label="低" value="低" />
          </el-select>
        </el-form-item>
        <el-form-item label="测试类型" prop="test_type" :rules="[{ required: true, message: '请输入测试类型', trigger: 'blur' }]">
          <el-input v-model="editForm.test_type" placeholder="请输入测试类型" />
        </el-form-item>
        <el-form-item label="前置条件" prop="pre_condition">
          <el-input v-model="editForm.pre_condition" type="textarea" :rows="3" placeholder="请输入前置条件" />
        </el-form-item>
        <el-form-item label="测试步骤" prop="steps">
          <el-input v-model="editForm.stepsStr" type="textarea" :rows="5" placeholder="请输入测试步骤，每行一个步骤" />
        </el-form-item>
        <el-form-item label="预期结果" prop="expected_result">
          <el-input v-model="editForm.expected_result" type="textarea" :rows="3" placeholder="请输入预期结果" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateTestCase" :loading="updating">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { TestCaseDetail, TreeNode } from "@/api/modules/service-llm/demand-doc";
import {
  api_getTestCaseByFunctionId,
  api_changeTestCaseLock,
  api_deleteTestCase,
  api_generateTestCaseOne,
  api_getFunctionDetailById,
  api_updateTestCase
} from "@/api/modules/service-llm/demand-doc";
import { ref, watch, onUnmounted } from "vue";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";

interface TestCaseDetailTable extends TestCaseDetail {
  id: string;
  functionId: string;
  isLocked: number;
  stepsStr: string;
}

const props = defineProps<{
  selectedNode: TreeNode | null;
}>();

const emit = defineEmits(["switchComponent"]);
const testCaseDetailList = ref<TestCaseDetailTable[]>([]);
const lockingCaseId = ref<string>("");
const selectedTestCases = ref<TestCaseDetailTable[]>([]);
const deletingIds = ref<string[]>([]);
const batchDeleting = ref(false);
const generateDialogVisible = ref(false);
const generating = ref(false);
// 编辑测试案例相关变量
const editDialogVisible = ref(false);
const editForm = ref<Partial<TestCaseDetailTable>>({});
const editFormRef = ref<InstanceType<typeof ElForm>>();
const updating = ref(false);
const currentEditingId = ref("");
const generateForm = ref({
  prompt: ""
});
const caseStatus = ref(0); // 0: 正常, 1: 生成中
let pollingTimer: ReturnType<typeof setInterval> | null = null; // 轮询定时器

const switchComponent = () => {
  emit("switchComponent", "DemandDetail");
};

// 处理用例评审按钮点击
const handleCaseReview = () => {
  emit("switchComponent", "TestCaseReview");
};

const getPriorityType = (priority: string | undefined): "danger" | "warning" | "info" | undefined => {
  if (!priority) return undefined;
  if (priority === "高") {
    return "danger";
  }
  if (priority === "中") {
    return "warning";
  }
  if (priority === "低") {
    return "info";
  }
};

const handleToggleLock = (row: TestCaseDetailTable) => {
  // 设置正在处理的ID
  lockingCaseId.value = row.id;

  // 调用API更改锁定状态
  api_changeTestCaseLock(row.id)
    .then(res => {
      if (res.respCode === 2000) {
        row.isLocked = row.isLocked === 1 ? 0 : 1;
        fetchTestCases();
        ElMessage.success(`${row.isLocked === 1 ? "锁定" : "解锁"}成功`);
      } else {
        ElMessage.error(`${row.isLocked === 1 ? "解锁" : "锁定"}失败: ${res.respMsg || "未知错误"}`);
      }
    })
    .catch(err => {
      ElMessage.error(`操作失败: ${err.message}`);
    })
    .finally(() => {
      // 清除正在处理的ID
      lockingCaseId.value = "";
    });
};

// 开始轮询检查测试案例生成状态
const startPolling = () => {
  // 清除可能存在的旧定时器
  if (pollingTimer) {
    clearInterval(pollingTimer);
  }

  // 设置新的轮询定时器，每5秒检查一次
  pollingTimer = setInterval(() => {
    // 只检查功能点详情以获取caseStatus
    api_getFunctionDetailById(props.selectedNode!.function_id!).then(res => {
      if (res.respCode === 2000 && res.respData) {
        // 更新状态
        caseStatus.value = res.respData.caseStatus;

        // 如果状态变为0（生成完成），刷新测试案例列表并停止轮询
        if (caseStatus.value === 0) {
          fetchTestCases();
          stopPolling();
          ElMessage.success("测试案例生成完成");
        }
      }
    });
  }, 5000); // 5秒轮询一次
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
};

const fetchCaseStatus = async function () {
  await api_getFunctionDetailById(props.selectedNode!.function_id!).then(res => {
    if (res.respCode === 2000) {
      caseStatus.value = res.respData.caseStatus;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const fetchTestCases = function () {
  api_getTestCaseByFunctionId(props.selectedNode!.function_id!).then(res => {
    if (res.respCode === 2000) {
      if (!res.respData.data) {
        testCaseDetailList.value = [];
        return;
      }
      // 将每个测试案例的statusMeta解析为TestCase对象
      testCaseDetailList.value = res.respData.data.map(item => {
        const statusMeta = JSON.parse(item.statusMeta as string);
        return {
          id: item.id,
          functionId: item.functionId,
          isLocked: item.isLocked,
          case_id: statusMeta.case_id || "",
          case_name: statusMeta.case_name || "",
          priority: statusMeta.priority || "",
          test_type: statusMeta.test_type || "",
          pre_condition: statusMeta.pre_condition || "",
          steps: statusMeta.steps || [],
          expected_result: statusMeta.expected_result || "",
          actual_result: statusMeta.actual_result || ""
        } as TestCaseDetailTable;
      });
    } else {
      testCaseDetailList.value = [];
      ElMessage.error(res.respMsg);
    }
  });
};

// 处理选择变化
const handleSelectionChange = (selection: TestCaseDetailTable[]) => {
  selectedTestCases.value = selection;
};

// 处理单个删除
const handleSingleDelete = (row: TestCaseDetailTable) => {
  // 检查测试案例是否锁定
  if (row.isLocked === 1) {
    ElMessage.warning("无法删除已锁定的测试案例，请先解锁后再删除");
    return;
  }

  ElMessageBox.confirm(`确定要删除测试案例"${row.case_name}"吗？此操作不可逆。`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deletingIds.value.push(row.id);
    api_deleteTestCase([row.id])
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("删除成功");
          fetchTestCases();
        } else {
          ElMessage.error(`删除失败: ${res.respMsg || "未知错误"}`);
        }
      })
      .catch(err => {
        ElMessage.error(`操作失败: ${err.message}`);
      })
      .finally(() => {
        const index = deletingIds.value.indexOf(row.id);
        if (index !== -1) {
          deletingIds.value.splice(index, 1);
        }
      });
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  // 检查是否有锁定的测试案例
  const lockedCases = selectedTestCases.value.filter(item => item.isLocked === 1);
  if (lockedCases.length > 0) {
    const lockedCaseIds = lockedCases.map(item => item.case_id).join("、");
    ElMessage.warning(`无法删除已锁定的测试案例，请先解锁以下案例后再删除：\n${lockedCaseIds}`);
    return;
  }

  const selectedIds = selectedTestCases.value.map(item => item.id);
  const selectedCaseIds = selectedTestCases.value.map(item => item.case_id).join("、");

  ElMessageBox.confirm(
    `确定要删除选定的${selectedTestCases.value.length}个测试案例吗？此操作不可逆。\n${selectedCaseIds}`,
    "批量删除警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    batchDeleting.value = true;
    api_deleteTestCase(selectedIds)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success(`成功删除${selectedTestCases.value.length}个测试案例`);
          // 清空选择
          selectedTestCases.value = [];
        } else {
          ElMessage.error(`批量删除失败: ${res.respMsg || "未知错误"}`);
        }
      })
      .finally(() => {
        fetchTestCases();
        batchDeleting.value = false;
      });
  });
};

// 打开生成测试案例弹窗
const openGenerateDialog = () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法生成测试案例：未找到功能点ID");
    return;
  }
  generateForm.value.prompt = "";
  generateDialogVisible.value = true;
};

// 打开编辑弹窗
const openEditDialog = (row: TestCaseDetailTable) => {
  currentEditingId.value = row.id;
  // 将步骤数组转换为换行分隔的字符串
  const stepsStr = row.steps.join("\n");
  editForm.value = {
    ...row,
    stepsStr: stepsStr
  };
  editDialogVisible.value = true;
};

// 处理更新测试案例
const handleUpdateTestCase = async () => {
  if (!editFormRef.value) return;
  await editFormRef.value.validate();
  updating.value = true;
  // 构建statusMeta对象
  const statusMeta = {
    case_id: editForm.value.case_id,
    case_name: editForm.value.case_name,
    priority: editForm.value.priority,
    test_type: editForm.value.test_type,
    pre_condition: editForm.value.pre_condition,
    steps: editForm.value.stepsStr?.split("\n") || [],
    expected_result: editForm.value.expected_result
  };
  // 调用更新接口
  api_updateTestCase(currentEditingId.value, JSON.stringify(statusMeta))
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("测试案例更新成功");
        editDialogVisible.value = false;
        fetchTestCases(); // 刷新列表
      } else {
        ElMessage.error(`更新失败: ${res.respMsg || "未知错误"}`);
      }
    })
    .finally(() => {
      updating.value = false;
    });
};

// 处理生成测试案例
const handleGenerateTestCase = () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法生成测试案例：未找到功能点ID");
    return;
  }

  generating.value = true;
  api_generateTestCaseOne(props.selectedNode.function_id, generateForm.value.prompt || "")
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("测试案例生成请求已提交");
        generateDialogVisible.value = false;
        // 立即设置为生成中状态，不等待fetchTestCases的结果
        caseStatus.value = 1;
        setTimeout(() => {
          startPolling(); // 开始轮询检查状态
        }, 2000);
      } else {
        ElMessage.error(`生成测试案例失败: ${res.respMsg || "未知错误"}`);
      }
    })
    .finally(() => {
      generating.value = false;
    });
};

// 添加对 selectedNode 的监听
watch(
  () => props.selectedNode,
  async (newNode, oldNode) => {
    // 检查是否是真正的节点变更（避免相同节点的重复请求）
    if (newNode?.id !== oldNode?.id && newNode?.function_id) {
      await fetchCaseStatus();
      if (caseStatus.value === 1) {
        startPolling();
      }
      fetchTestCases();
    }
  },
  { immediate: true } // 立即执行一次
);

// 组件卸载时清除定时器
onUnmounted(() => {
  stopPolling();
});
</script>

<style lang="scss" scoped>
.content-container {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
  height: 100%;
  position: relative;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    background-color: #fff;

    &.fixed {
      position: sticky;
      top: 0;
      z-index: 999;
      padding: 15px 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background: linear-gradient(to right, #ffffff, #f8f9fa);
    }

    .content-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #409eff, #67c23a);
        border-radius: 2px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      .el-button {
        padding: 8px 20px;
        font-weight: 500;
      }
    }
  }

  .content-body {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    :deep(.el-table) {
      --el-table-border-color: #ebeef5;
      --el-table-header-bg-color: #f5f7fa;
      // 自动调整列宽
      table-layout: auto !important;
      word-break: break-all;
    }

    .test-steps-list {
      margin: 0;
      padding-left: 0;
      font-size: 14px;
      line-height: 1.8;
      color: #444;
      word-break: break-all;
      white-space: pre-line;
    }

    .test-steps-list.no-marker > div {
      margin-left: 0;
      padding-left: 0;
    }

    .expected-result-cell {
      white-space: pre-line;
      word-break: break-all;
      font-size: 14px;
      line-height: 1.7;
    }

    .pre-condition-cell {
      white-space: pre-line;
      word-break: break-all;
      font-size: 14px;
      line-height: 1.7;
    }

    // 行高优化
    :deep(.el-table__row) {
      height: auto !important;
      min-height: 48px;
    }

    :deep(.el-table__cell) {
      vertical-align: top;
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .generating-status {
      margin-bottom: 20px;

      .generating-status-content {
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        text-align: center;

        span {
          color: #606266;
          font-size: 14px;
        }

        :deep(.el-progress) {
          margin: 5px 0;
          width: 80%;
        }
      }

      :deep(.el-alert) {
        border-radius: 4px;
        background: linear-gradient(to right, #f0f9ff, #e6f7ff);
        border-left: 4px solid #409eff;

        .el-alert__title {
          font-weight: 600;
          font-size: 16px;
        }

        .el-alert__icon {
          color: #409eff;
          font-size: 18px;
        }

        .el-alert__content {
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
