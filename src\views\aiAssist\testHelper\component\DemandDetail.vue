<template>
  <div class="content-container">
    <!--选择节点-->
    <div v-if="selectedNode">
      <!--内容头-->
      <div class="content-header fixed">
        <h3 class="content-title">{{ selectedNode.title }}</h3>
        <div class="action-buttons" v-if="isLeafNode(selectedNode)">
          <el-button type="primary" @click="handleEdit" v-if="!isEditing">编辑</el-button>
          <template v-else>
            <el-button type="success" @click="handleSave">保存</el-button>
            <el-button @click="handleCancel">取消</el-button>
          </template>
          <el-button type="info" @click="switchComponent">测试案例</el-button>
        </div>
      </div>

      <!--内容体-->
      <div class="content-body">
        <!--如果选中的是第三层叶子节点且节点有内容-->
        <div v-if="functionDetail.length > 0">
          <div v-for="(item, index) in functionDetail" :key="index" class="markdown-item">
            <!--内容标题-->
            <h4 class="markdown-title">
              <template v-if="isEditing">
                <el-input v-model="editingData[index].title" placeholder="请输入标题" />
              </template>
              <template v-else>
                {{ item.title }}
              </template>
            </h4>

            <!--内容体-->
            <div class="markdown-content">
              <template v-if="isEditing">
                <el-input v-model="editingData[index].content" type="textarea" :rows="4" placeholder="请输入内容" />
              </template>
              <template v-else>
                <div class="markdown-text">{{ item.content }}</div>
              </template>
            </div>
          </div>
        </div>

        <!--选中的不是第三层叶子节点-->
        <div v-else-if="selectedNode.children && selectedNode.children.length > 0">
          <div v-for="child in selectedNode.children" :key="child.id" class="child-node">
            <h4 class="child-title">{{ child.title }}</h4>
            <div v-if="child.children && child.children.length > 0" class="nested-children">
              <div v-for="grandChild in child.children" :key="grandChild.id" class="grand-child-node">
                <h5 class="grand-child-title">{{ grandChild.title }}</h5>
              </div>
            </div>
          </div>
        </div>

        <!--选中节点无内容-->
        <div v-else class="no-content">
          <p>该节点没有详细内容</p>
        </div>
      </div>
    </div>

    <!--未选择节点-->
    <div v-else class="no-content">
      <p>请从左侧选择一个节点查看详细内容</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  api_getFunctionDetailById,
  api_updateFunctionPoint,
  FunctionDetailItem,
  TreeNode
} from "@/api/modules/service-llm/demand-doc";

const props = defineProps<{
  selectedNode: TreeNode | null;
}>();

const emit = defineEmits(["switchComponent"]);

const functionDetail = ref<FunctionDetailItem[]>([]);
const isEditing = ref(false);
const editingData = ref<FunctionDetailItem[]>([]);

// 判断是否为叶子节点（第三层节点）
const isLeafNode = (node: TreeNode) => {
  return node.function_id && (!node.children || node.children.length === 0);
};

// 点击编辑按钮
const handleEdit = () => {
  isEditing.value = true;
  editingData.value = JSON.parse(JSON.stringify(functionDetail.value));
};

// 处理取消编辑
const handleCancel = () => {
  isEditing.value = false;
  editingData.value = [];
};

const fetchFunctionDetail = (functionId: string) => {
  api_getFunctionDetailById(functionId).then(res => {
    if (res.respCode === 2000) {
      functionDetail.value = res.respData.content;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 处理保存编辑
const handleSave = async () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法保存：未找到功能点ID");
    return;
  }

  const content = JSON.stringify(editingData.value);
  const functionId = props.selectedNode.function_id;
  api_updateFunctionPoint(functionId, content).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("保存成功");
      isEditing.value = false;
      // 重新获取数据
      fetchFunctionDetail(props.selectedNode!.function_id!);
    } else {
      ElMessage.error(res.respMsg || "保存失败");
    }
  });
};

// 处理测试案例按钮点击
const switchComponent = () => {
  emit("switchComponent", "TestCaseList");
};

// 监听 selectedNode 变化
const watchSelectedNode = (node: TreeNode | null) => {
  if (node?.function_id) {
    fetchFunctionDetail(node.function_id);
  } else {
    functionDetail.value = [];
  }
};

watch(() => props.selectedNode, watchSelectedNode, { immediate: true });
</script>

<style lang="scss" scoped>
.content-container {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
  height: 100%;
  position: relative;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    background-color: #fff;

    &.fixed {
      position: sticky;
      top: 0;
      z-index: 1;
      padding: 15px 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background: linear-gradient(to right, #ffffff, #f8f9fa);
    }

    .content-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #409eff, #67c23a);
        border-radius: 2px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      .el-button {
        padding: 8px 20px;
        font-weight: 500;
      }
    }
  }

  .content-body {
    padding-top: 10px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: #24292e;

    .markdown-item {
      margin-bottom: 24px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border: 1px solid #dcdfe6;

      .markdown-title {
        font-size: 1.5em;
        margin: 0;
        padding: 16px 20px;
        border-bottom: 2px solid #eaecef;
        color: #24292e;
        font-weight: 600;
        background: #f8f9fa;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }

      .markdown-content {
        padding: 20px;

        .markdown-text {
          white-space: pre-wrap;
          font-size: 14px;
          line-height: 1.6;
          color: #24292e;
          background: #f6f8fa;
          padding: 16px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
        }
      }
    }

    .child-node {
      margin-bottom: 20px;
      padding: 15px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      .child-title {
        font-size: 1.2em;
        color: #303133;
        margin: 0 0 10px 0;
      }

      .nested-children {
        margin-left: 20px;

        .grand-child-node {
          margin: 10px 0;
          padding: 10px;
          background: #f8f9fa;
          border-radius: 4px;

          .grand-child-title {
            font-size: 1em;
            color: #606266;
            margin: 0;
          }
        }
      }
    }
  }

  .no-content {
    text-align: center;
    color: #909399;
    margin-top: 20px;
  }
}
</style>
