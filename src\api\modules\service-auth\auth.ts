import http from "@/api";
import { SERVICE_AUTH } from "@/api/config/servicePort";

// 登录接口参数
export interface LoginForm {
  username: string;
  password: string;
}

// 登录响应结果
export interface LoginResult {
  token: string;
  tokenType: string;
  userType?: string;
}

// 用户信息
export interface UserInfo {
  id?: number;
  username: string;
  email?: string;
  phone?: string;
  status?: number;
  userType?: string;
  roleList?: RoleInfo[];
}

// 角色信息
export interface RoleInfo {
  id: number;
  roleName: string;
  roleCode: string;
  description?: string;
}

// 用户登录
export const api_login = (params: LoginForm) => {
  return http.post<LoginResult>(SERVICE_AUTH + "/api/login", params);
};

// 访客登录
export const api_guestLogin = () => {
  return http.post<LoginResult>(SERVICE_AUTH + "/api/guest-login");
};

// 获取用户信息
export const api_getUserInfo = () => {
  return http.get<UserInfo>(SERVICE_AUTH + "/api/user-info");
};

// 验证token
export const api_validateToken = () => {
  return http.post<{ valid: boolean; username?: string }>(SERVICE_AUTH + "/api/validate-token");
};

// 用户登出
export const api_logout = () => {
  return http.post<string>(SERVICE_AUTH + "/api/logout");
};

// 菜单权限相关接口

// 菜单项接口
export interface MenuItem {
  id: number;
  menuName: string;
  menuTitle: string;
  menuPath: string;
  componentPath?: string;
  redirectPath?: string;
  parentId: number;
  menuType: number;
  menuIcon?: string;
  sortOrder: number;
  permissionCode?: string;
  isEnabled: number;
  isHide: number;
  isAffix: number;
  isKeepAlive: number;
  isFull: number;
  isLink?: string;
  allowGuest: number;
  requiredRoles?: string;
  remark?: string;
  children?: MenuItem[];
  menuChildren?: MenuItem[];
}

// 获取用户菜单权限
export const api_getUserMenus = () => {
  return http.get<MenuItem[]>(SERVICE_AUTH + "/api/user-menus");
};

// 获取访客菜单
export const api_getGuestMenus = () => {
  return http.get<MenuItem[]>(SERVICE_AUTH + "/api/guest-menus");
};

// 检查菜单访问权限
export const api_checkMenuPermission = (menuPath: string) => {
  return http.get<boolean>(SERVICE_AUTH + "/api/check-menu-permission", { menuPath });
};
