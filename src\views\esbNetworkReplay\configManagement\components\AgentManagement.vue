<template>
  <div class="agent-management">
    <div class="operation-area">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input
            v-model="searchForm.serverName"
            placeholder="搜索服务器名称"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          ></el-input>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="openServerSelectDialog">
            <el-icon><Plus /></el-icon>
            新增服务器
          </el-button>
        </el-col>
      </el-row>
    </div>

    <div class="agent-table">
      <el-table :data="agentList" border v-loading="tableLoading">
        <el-table-column prop="serverName" label="服务器名称" width="200" />
        <el-table-column prop="serverIp" label="服务器IP" width="200" />
        <el-table-column prop="agentStatus" label="状态" width="150">
          <template #default="scope">
            <div class="status-cell">
              <el-tag :type="getAgentStatusType(scope.row.agentStatus)">
                {{ getAgentStatusText(scope.row.agentStatus) }}
              </el-tag>
              <el-button type="primary" link size="small" @click="checkAgentStatus(scope.row)" :loading="scope.row.checking">
                <el-icon><RefreshRight /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="服务器描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="380" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" :disabled="scope.row.agentStatus === 1" @click="openRecordDialog(scope.row)">
              开始记录
            </el-button>
            <el-button type="danger" size="small" :disabled="scope.row.agentStatus === 2" @click="stopRecord(scope.row)">
              停止记录
            </el-button>
            <el-button type="warning" size="small" @click="deployAgentScript(scope.row)" :loading="scope.row.deploying">
              探针部署
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pageInfo.pageNo"
          v-model:page-size="pageInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageInfo.total"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        >
        </el-pagination>
      </div>
    </div>

    <!-- 新增服务器对话框 -->
    <el-drawer
      v-model="serverDialog.visible"
      title="新增流量回放服务器"
      size="55%"
      direction="rtl"
      :show-close="true"
      destroy-on-close
    >
      <div class="drawer-content">
        <div>
          <div class="server-select-header">
            <el-input
              v-model="serverDialog.searchKeyword"
              placeholder="搜索服务器"
              clearable
              prefix-icon="Search"
              @keyup.enter="searchAvailableServers"
              @clear="searchAvailableServers"
            ></el-input>
          </div>
          <div class="server-select-list" v-loading="serverDialog.loading">
            <el-table :data="availableServers" style="width: 100%" highlight-current-row>
              <el-table-column prop="serverName" label="服务器名称" min-width="140">
                <template #default="scope">
                  <div class="server-name-cell">
                    <el-icon><Monitor /></el-icon>
                    <span>{{ scope.row.serverName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="serverIp" label="IP地址" min-width="120"></el-table-column>
              <el-table-column prop="username" label="用户名" min-width="100"></el-table-column>
              <el-table-column prop="environment" label="环境" min-width="80">
                <template #default="scope">
                  <el-tag size="small" effect="plain">{{ scope.row.environment || "未设置" }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="180">
                <template #default="scope">
                  <el-button type="primary" @click="addServer(scope.row)" :loading="scope.row.adding">添加</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="serverDialog.pageInfo.pageNo"
                v-model:page-size="serverDialog.pageInfo.pageSize"
                layout="prev, pager, next"
                :total="serverDialog.pageInfo.total"
                @size-change="searchAvailableServers"
                @current-change="searchAvailableServers"
                background
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 开始记录对话框 -->
    <el-dialog v-model="recordDialog.visible" title="开始流量记录" width="500px" destroy-on-close :close-on-click-modal="false">
      <el-form :model="recordForm" :rules="recordFormRules" ref="recordFormRef" label-width="120px">
        <el-form-item label="服务器信息">
          <div class="server-info">
            <span>
              <strong>{{ recordDialog.currentServer?.serverName }}</strong>
            </span>
            <span>({{ recordDialog.currentServer?.serverIp }})</span>
          </div>
        </el-form-item>
        <el-form-item label="网络接口" prop="networkInterface">
          <el-select
            v-model="recordForm.networkInterface"
            placeholder="请选择网络接口"
            style="width: 100%"
            :loading="recordDialog.loadingInterfaces"
          >
            <el-option v-for="item in recordDialog.networkInterfaces" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="端口号" prop="port">
          <el-input-number v-model="recordForm.port" :min="1" :max="65535" controls-position="right" style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="recordDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitStartRecord" :loading="recordDialog.submitting"> 开始记录 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  api_fetchAgentList,
  api_startRecord,
  api_stopRecord,
  api_addAgentServer,
  api_checkAgentStatus,
  api_deployAgentScript,
  EnrAgentServerDto,
  api_getNicList
} from "@/api/modules/service-enr/enrAgent";
import { api_getByPageInfo, ServerInfo } from "@/api/modules/service-server-operation/server-management";
import { Monitor, Plus, RefreshRight } from "@element-plus/icons-vue";

// 表格加载状态
const tableLoading = ref(false);

// 探针列表
const agentList = ref<EnrAgentServerDto[]>([]);

// 分页信息
const pageInfo = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});

// 搜索条件
const searchForm = ref<Partial<EnrAgentServerDto>>({
  serverName: ""
});

// 服务器选择对话框
const serverDialog = ref({
  visible: false,
  searchKeyword: "",
  loading: false,
  pageInfo: {
    pageNo: 1,
    pageSize: 10,
    total: 0
  }
});

// 开始记录对话框
const recordDialog = ref({
  visible: false,
  currentServer: null as EnrAgentServerDto | null,
  submitting: false,
  loadingInterfaces: false,
  networkInterfaces: [""]
});

// 记录表单
const recordForm = ref({
  networkInterface: "",
  port: 8090
});

// 记录表单校验规则
const recordFormRules = ref<FormRules>({
  networkInterface: [{ required: true, message: "请选择网络接口", trigger: "change" }],
  port: [
    { required: true, message: "请输入端口号", trigger: "blur" },
    { type: "number", min: 1, max: 65535, message: "端口号范围为1-65535", trigger: "blur" }
  ]
});

// 记录表单引用
const recordFormRef = ref<FormInstance>();

// 扩展服务器信息类型
interface ServerInfoExtended extends ServerInfo {
  adding: boolean;
}

// 可选服务器列表
const availableServers = ref<ServerInfoExtended[]>([]);

// 获取探针状态样式
const getAgentStatusType = (status: number) => {
  switch (status) {
    case 2:
      return "info";
    case 1:
      return "success";
    case 0:
      return "danger";
    case -1:
    default:
      return "info";
  }
};

// 获取探针状态文本
const getAgentStatusText = (status: number) => {
  switch (status) {
    case 2:
      return "空闲";
    case 1:
      return "记录中";
    case 0:
      return "异常";
    case -1:
      return "未检测";
    default:
      return "未知";
  }
};

// 检查探针状态
const checkAgentStatus = (enrAgentServerDto: EnrAgentServerDto) => {
  if (!enrAgentServerDto || !enrAgentServerDto.id) return;

  // 设置正在检测状态
  enrAgentServerDto.checking = true;

  api_checkAgentStatus(enrAgentServerDto.id)
    .then(res => {
      if (res.respCode === 2000) {
        enrAgentServerDto.agentStatus = res.respData;
        ElMessage.success("状态检测完成");
      } else {
        ElMessage.error(res.respMsg || "状态检测失败");
      }
    })
    .finally(() => {
      enrAgentServerDto.checking = false;
    });
};

// 部署探针脚本
const deployAgentScript = (agent: EnrAgentServerDto) => {
  if (!agent || !agent.id) return;

  // 设置正在部署状态
  agent.deploying = true;

  api_deployAgentScript(agent.id)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success(`服务器 ${agent.serverName} 脚本部署成功`);
      } else {
        ElMessage.error(res.respMsg || "脚本部署失败");
      }
    })
    .finally(() => {
      agent.deploying = false;
    });
};

// 获取探针列表
const fetchAgents = (
  pageNo = pageInfo.value.pageNo,
  pageSize = pageInfo.value.pageSize,
  enrAgentServerDto: Partial<EnrAgentServerDto> = {}
) => {
  tableLoading.value = true;

  api_fetchAgentList(pageNo, pageSize, enrAgentServerDto)
    .then(res => {
      if (res.respCode === 2000) {
        agentList.value = res.respData.pageData || [];
        pageInfo.value.total = res.respData.totalCount || 0;
      } else {
        ElMessage.error(res.respMsg || "获取探针列表失败");
      }
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

// 搜索处理
const handleSearch = () => {
  pageInfo.value.pageNo = 1;
  fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
};

// 处理每页数量变化
const handleSizeChange = (size: number) => {
  pageInfo.value.pageSize = size;
  fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  pageInfo.value.pageNo = page;
  fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
};

const fetchNicList = function (serverInfoId: string) {
  api_getNicList(serverInfoId).then(res => {
    if (res.respCode === 2000) {
      recordDialog.value.networkInterfaces = res.respData;
    } else {
      ElMessage.error(res.respMsg || "获取网络接口列表失败");
    }
  });
};

// 打开记录对话框
const openRecordDialog = async (server: EnrAgentServerDto) => {
  recordDialog.value.visible = true;
  recordDialog.value.currentServer = server;
  recordForm.value.networkInterface = "";
  recordForm.value.port = 8090;

  recordDialog.value.loadingInterfaces = true;
  await fetchNicList(server.serverInfoId);
  recordDialog.value.loadingInterfaces = false;
};

// 提交开始记录
const submitStartRecord = () => {
  if (!recordFormRef.value) return;

  recordFormRef.value.validate(valid => {
    if (valid && recordDialog.value.currentServer) {
      recordDialog.value.submitting = true;

      // 调用API开始记录
      const serverId = recordDialog.value.currentServer.id;
      const networkInterface = recordForm.value.networkInterface;
      const port = recordForm.value.port.toString();

      api_startRecord(serverId, networkInterface, port)
        .then(res => {
          if (res.respCode === 2000) {
            ElMessage.success(`探针 ${recordDialog.value.currentServer?.serverName} 已开始记录流量`);
            recordDialog.value.visible = false;
            fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
          } else {
            ElMessage.error(res.respMsg || "开始记录失败");
          }
        })
        .finally(() => {
          recordDialog.value.submitting = false;
        });
    }
  });
};

// 停止记录
const stopRecord = (enrAgentServerDto: EnrAgentServerDto) => {
  tableLoading.value = true;

  api_stopRecord(enrAgentServerDto.id)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success(`探针 ${enrAgentServerDto.serverName} 已停止记录`);
        fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
      } else {
        ElMessage.error(res.respMsg || "停止记录失败");
        tableLoading.value = false;
      }
    })
    .catch(() => {
      tableLoading.value = false;
    });
};

// 打开服务器选择对话框
const openServerSelectDialog = () => {
  serverDialog.value.visible = true;
  serverDialog.value.searchKeyword = "";
  serverDialog.value.pageInfo.pageNo = 1;
  searchAvailableServers();
};

// 搜索可用服务器
const searchAvailableServers = () => {
  serverDialog.value.loading = true;
  api_getByPageInfo(serverDialog.value.pageInfo.pageNo, serverDialog.value.pageInfo.pageSize, {
    serverName: serverDialog.value.searchKeyword
  })
    .then(res => {
      if (res.respCode === 2000) {
        availableServers.value = res.respData.pageData.map(server => {
          return {
            ...server,
            adding: false
          };
        });
        serverDialog.value.pageInfo.total = res.respData.totalCount;
      } else {
        ElMessage.error(res.respMsg || "获取服务器列表失败");
      }
    })
    .finally(() => {
      serverDialog.value.loading = false;
    });
};

// 添加服务器
const addServer = (server: ServerInfoExtended) => {
  if (!server || !server.id) {
    ElMessage.warning("服务器信息不完整");
    return;
  }

  server.adding = true;

  // 调用后端接口将服务器添加为流量回放服务器
  api_addAgentServer(server.id)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success(`服务器 ${server.serverName || server.serverIp} 已添加为流量回放服务器`);

        // 关闭对话框
        serverDialog.value.visible = false;

        // 刷新探针列表
        fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
      } else {
        ElMessage.error(res.respMsg || "添加服务器失败");
      }
    })
    .finally(() => {
      server.adding = false;
    });
};

// 页面加载时获取探针列表
onMounted(() => {
  fetchAgents(pageInfo.value.pageNo, pageInfo.value.pageSize, searchForm.value);
});
</script>

<style scoped lang="scss">
.agent-management {
  padding: 10px 0;

  .operation-area {
    margin-bottom: 20px;
  }

  .agent-table {
    margin-top: 20px;

    .status-cell {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .text-right {
    text-align: right;
  }

  .server-select-header {
    margin-bottom: 20px;
  }

  .server-select-list {
    margin-top: 20px;

    .server-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .server-info {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
