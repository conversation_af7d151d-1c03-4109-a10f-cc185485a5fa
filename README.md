# 一体化测试平台

### 项目简介 📖

基于 Vue 3.4、TypeScript、Vite 5、Pinia、Element Plus 等前端技术栈开发。平台集成了混沌工程测试、ESB流量回放、协同办公报表比对、有权机关报文包生成、AI助理等多种功能。

### 技术特性 🛠️

- **技术栈**: Vue 3.4 + TypeScript + Vite 5 + Pinia + Element Plus
- **组件化开发**: 基于 Composition API 的组件化开发模式
- **状态管理**: 使用 Pinia 进行状态管理，支持数据持久化
- **路由管理**: 动态路由配置，支持权限控制和路由懒加载
- **HTTP 封装**: 完整的 Axios 二次封装，支持请求拦截、错误处理、取消请求等
- **图表可视化**: 集成 ECharts 和 AntV G6，支持丰富的数据可视化
- **代码规范**: 集成 ESLint、Prettier、Stylelint 等代码规范工具
- **类型安全**: 完整的 TypeScript 类型定义，保证代码质量

### 环境要求 📋

- **Node.js**: >= 16.0.0
- **包管理器**: npm / yarn / pnpm（推荐使用 npm）
- **浏览器**: 支持现代浏览器，不支持 IE

### 安装和启动 🚀

#### 1. 克隆项目

```bash
git clone <repository-url>
cd integrated-tester-web
```

#### 2. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

#### 3. 启动开发服务器

```bash
# 启动开发服务器
npm run dev
# 或
npm run serve
```

项目将在 `http://localhost:11110` 启动

#### 4. 构建项目

```bash
# 开发环境构建
npm run build:dev

# 测试环境构建
npm run build:test

# 生产环境构建
npm run build:pro
```

### 开发指南 👨‍💻

#### 代码规范

项目集成了完整的代码规范工具链：

```bash
# ESLint 代码检查
npm run lint:eslint

# Prettier 代码格式化
npm run lint:prettier

# Stylelint 样式格式化
npm run lint:stylelint

# TypeScript 类型检查
npm run type:check
```

#### 提交规范

```bash
# 规范化提交（会自动执行代码检查）
npm run commit
```

#### 预览构建结果

```bash
# 构建并预览
npm run preview
```

### 技术栈 🛠️

#### 核心框架
- **Vue 3.4** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供静态类型检查
- **Vite 5** - 下一代前端构建工具

#### UI 组件库
- **Element Plus 2.9.4** - 基于 Vue 3 的组件库
- **@element-plus/icons-vue** - Element Plus 图标库
- **@icon-park/vue-next** - 字节跳动图标库

#### 状态管理与路由
- **Pinia 2.1.7** - Vue 的状态管理库
- **pinia-plugin-persistedstate** - Pinia 持久化插件
- **Vue Router 4.3.0** - Vue 官方路由管理器

#### HTTP 与工具库
- **Axios 1.7.4** - HTTP 客户端
- **Day.js 1.11.10** - 轻量级日期处理库
- **CryptoJS 4.2.0** - 加密库
- **MD5 2.3.0** - MD5 加密
- **qs 6.11.2** - 查询字符串解析

#### 图表与可视化
- **ECharts 5.5.0** - 数据可视化图表库
- **echarts-liquidfill 3.1.0** - ECharts 水球图插件
- **@antv/g6 4.8.24** - 图可视化引擎

#### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Stylelint** - CSS 代码检查
- **Sass** - CSS 预处理器
- **PostCSS** - CSS 后处理器

### 项目结构 📁

```text
integrated-tester-web
├─ build/                  # Vite 构建配置
│  ├─ getEnv.ts           # 环境变量处理
│  ├─ plugins.ts          # Vite 插件配置
│  └─ proxy.ts            # 开发代理配置
├─ public/                 # 静态资源文件
│  ├─ bqd.ico             # 网站图标
│  ├─ logo.png            # Logo 图片
│  └─ vue.svg             # Vue 图标
├─ src/
│  ├─ api/                # API 接口管理
│  │  ├─ config/          # 接口配置
│  │  ├─ modules/         # 接口模块
│  │  │  ├─ service-auth/           # 认证服务接口
│  │  │  ├─ service-esb-data/       # ESB 数据服务接口
│  │  │  ├─ service-integrated-tester/ # 一体化测试服务接口
│  │  │  ├─ service-server-operation/  # 服务器操作接口
│  │  │  ├─ esbNetworkReplay/       # ESB 网络回放接口
│  │  │  └─ scheduler/              # 定时任务接口
│  │  ├─ interface/       # 接口类型定义
│  │  └─ index.ts         # HTTP 客户端封装
│  ├─ assets/             # 静态资源
│  │  ├─ fonts/           # 字体文件
│  │  ├─ iconfont/        # 图标字体
│  │  └─ images/          # 图片资源
│  ├─ components/         # 全局组件
│  ├─ config/             # 全局配置
│  ├─ data/               # 静态数据
│  ├─ enums/              # 枚举定义
│  ├─ hooks/              # 组合式函数
│  ├─ layouts/            # 布局组件
│  ├─ routers/            # 路由配置
│  ├─ stores/             # Pinia 状态管理
│  │  ├─ config/          # 持久化配置
│  │  ├─ interface/       # 状态接口定义
│  │  └─ modules/         # 状态模块
│  ├─ styles/             # 全局样式
│  ├─ tools/              # 工具函数
│  ├─ typings/            # TypeScript 类型声明
│  ├─ utils/              # 工具库
│  ├─ views/              # 页面组件
│  │  ├─ home/            # 首页
│  │  ├─ login/           # 登录页
│  │  ├─ chaos/           # 混沌工程测试
│  │  ├─ esbNetworkReplay/ # ESB 网络回放
│  │  ├─ financialStatement/ # 协同办公报表比对
│  │  ├─ authorityPacket/ # 有权机关报文包生成
│  │  ├─ aiAssist/        # AI 辅助测试
│  │  ├─ ServerManagement/ # 服务器管理
│  │  ├─ schedulerManagement/ # 定时任务管理
│  │  ├─ projectManagement/ # 项目管理
│  │  ├─ tool/            # 工具模块
│  │  └─ link/            # 系统链路
│  ├─ App.vue             # 根组件
│  ├─ main.ts             # 应用入口
│  └─ vite-env.d.ts       # Vite 类型声明
├─ .env                   # 环境变量配置
├─ .env.development       # 开发环境配置
├─ .env.production        # 生产环境配置
├─ .env.test              # 测试环境配置
├─ index.html             # HTML 模板
├─ package.json           # 项目依赖配置
├─ postcss.config.cjs     # PostCSS 配置
├─ tsconfig.json          # TypeScript 配置
├─ vite.config.ts         # Vite 配置
└─ README.md              # 项目说明文档
```

### 环境配置 ⚙️

#### 开发环境配置

项目支持多环境配置，通过不同的 `.env` 文件管理：

- `.env` - 通用配置
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置
- `.env.production` - 生产环境配置

#### 主要配置项

```bash
# 应用标题
VITE_GLOB_APP_TITLE=一体化测试平台

# 开发服务器端口
VITE_PORT=11110

# API 接口地址
VITE_API_URL=http://127.0.0.1:25999/api

# 路由模式 (hash | history)
VITE_ROUTER_MODE=hash

# 是否开启 PWA
VITE_PWA=false
```

### 浏览器支持 🌐

- **推荐浏览器**: Chrome 最新版本
- **支持浏览器**: 现代浏览器（Chrome、Firefox、Safari、Edge）
- **不支持**: Internet Explorer

| Chrome | Firefox | Safari | Edge |
|:------:|:-------:|:------:|:----:|
| ✅ 最新版本 | ✅ 最新版本 | ✅ 最新版本 | ✅ 最新版本 |

### 后端服务 🔗

项目依赖以下后端微服务：

- **service-auth** - 认证授权服务
- **service-integrated-tester** - 一体化测试核心服务
- **service-server-operation** - 服务器操作服务
- **service-esb-data** - ESB数据服务
- **service-enr** - ESB网络回放服务
- **service-llm** - 大语言模型服务
- **scheduler** - 定时任务调度服务

### 开发团队 👥

本项目由青岛银行科技部门开发和维护，专注于为金融行业提供专业的测试解决方案。

### 版本信息 📝

- **当前版本**: v1.2.0
- **Node.js 要求**: >= 16.0.0
- **构建工具**: Vite 5.4.6
- **框架版本**: Vue 3.4.21

---

© 2024 青岛银行一体化测试平台. 保留所有权利.
