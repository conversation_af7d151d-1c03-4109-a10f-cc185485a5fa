import http from "@/api";
import { SERVICE_LLM } from "@/api/config/servicePort";
import { Page } from "@/api/interface";

export interface DemandDoc {
  id: string;
  name: string;
  createTime: string;
  userId: string;
  isProcessing: number;
  statusMeta: string;
}

// 定义节点类型接口
export interface TreeNode {
  id: string;
  title: string;
  function_id?: string;
  content?: string;
  children?: TreeNode[];
}

export interface FunctionDetailItem {
  title: string;
  content: string;
}

export interface FunctionDetailResponse {
  content: FunctionDetailItem[];
  caseStatus: number;
}

export interface TestCaseResponse {
  status: string;
  data: TestCaseOverview[];
}

export interface TestCaseOverview {
  id: string;
  functionId: string;
  isLocked: number;
  isReviewed: number; // 1表示评审通过，0表示未评审或评审不通过
  statusMeta: string | TestCaseDetail;
}

export interface TestCaseDetail {
  case_id: string;
  case_name: string;
  priority: string;
  test_type: string;
  pre_condition: string;
  steps: string[];
  expected_result: string;
  actual_result: string;
}

/**
 * 文件管理接口
 */

// 文档上传
export const api_upload = (): string => {
  return `${import.meta.env.VITE_API_URL}${SERVICE_LLM}/demandDoc/upload`;
};

// 获取已上传文件列表
export const api_getFileList = (page: number, size: number) => {
  return http.get<Page<DemandDoc[]>>(`${SERVICE_LLM}/demandDoc/getFileList`, { page, size });
};

// 根据ID删除文件
export const api_deleteFileById = (id: string) => {
  return http.get(`${SERVICE_LLM}/demandDoc/deleteFileById`, { id });
};

/**
 * 功能点相关接口
 */

// 获取解析后的功能点 树结构
export const api_getDetailById = (id: string) => {
  return http.get<TreeNode[]>(`${SERVICE_LLM}/demandDoc/getDetailById`, { id });
};

// 根据功能点ID获取功能点详情
export const api_getFunctionDetailById = (id: string) => {
  return http.get<FunctionDetailResponse>(`${SERVICE_LLM}/demandDoc/getFunctionDetailById`, { id });
};

// 编辑功能点详情
export const api_updateFunctionPoint = (id: string, content: string) => {
  return http.post(`${SERVICE_LLM}/demandDoc/updateFunctionPoint`, { id, content });
};

export const api_deleteFunctionPointsById = (functionIdList: string[]) => {
  return http.post(`${SERVICE_LLM}/demandDoc/deleteFunctionPointsById`, functionIdList);
}

/**
 * 测试案例相关接口
 */

// 根据功能点id获取测试案例
export const api_getTestCaseByFunctionId = (functionId: string) => {
  return http.get<TestCaseResponse>(`${SERVICE_LLM}/demandDoc/getTestCaseByFunctionId`, { functionId });
};

// 生成测试案例
export const api_generateTestCase = (functionIdList: string[]) => {
  return http.post(`${SERVICE_LLM}/demandDoc/generateTestCase`, functionIdList);
};

// 单个功能点生成测试案例（带提示词）
export const api_generateTestCaseOne = (functionId: string, prompt: string) => {
  return http.post(`${SERVICE_LLM}/demandDoc/generateTestCaseOne?id=${functionId}`, prompt);
};

// 测试案例加锁/解锁
export const api_changeTestCaseLock = (id: string) => {
  return http.get(`${SERVICE_LLM}/demandDoc/changeTestCaseLock`, { id });
};

// 根据id列表删除测试案例
export const api_deleteTestCase = (idList: string[]) => {
  return http.post(`${SERVICE_LLM}/demandDoc/deleteTestCaseById`, idList);
};

export const api_changeTestCaseReview = (id: string) => {
  return http.get(`${SERVICE_LLM}/demandDoc/changeTestCaseReview`, { id });
};

// 修改测试用例
export const api_updateTestCase = (id: string, content: string) => {
  return http.post(`${SERVICE_LLM}/demandDoc/updateTestCase`, { id, content });
}
