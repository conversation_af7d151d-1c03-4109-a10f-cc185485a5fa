import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import {
  api_login,
  api_guestLogin,
  api_getUserInfo,
  api_logout,
  LoginForm,
  UserInfo,
  LoginResult
} from "@/api/modules/service-auth/auth";
import { ElMessage } from "element-plus";
import router from "@/routers";
import { LOGIN_URL } from "@/config";
import CryptoJS from "crypto-js";
import { userAuthPersist } from "@/stores/config/persist";
import { useMenuStore } from "@/stores/modules/menu";

export const useUserStore = defineStore("user", {
  state: (): UserState => ({
    token: "",
    userInfo: {},
    roles: [],
    isGuest: false
  }),
  getters: {
    // 获取token
    getToken: state => state.token,
    // 获取用户信息
    getUserInfo: state => state.userInfo,
    // 获取用户角色
    getRoles: state => state.roles,
    // 是否为访客
    getIsGuest: state => state.isGuest,
    // 是否已登录
    getIsLoggedIn: state => !!state.token
  },
  actions: {
    // 设置token
    setToken(token: string) {
      this.token = token;
    },

    // 设置用户信息
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo;
      // 提取用户角色
      if (userInfo.roleList) {
        this.roles = userInfo.roleList.map(role => role.roleCode);
      }
    },

    // 设置访客状态
    setIsGuest(isGuest: boolean) {
      this.isGuest = isGuest;
    },

    // 用户登录
    async login(loginForm: LoginForm): Promise<LoginResult> {
      // 创建一个新对象，避免直接修改原始对象
      const loginData = {
        username: loginForm.username,
        password: CryptoJS.MD5(loginForm.password).toString().toUpperCase()
      };
      const res = await api_login(loginData);
      if (res.respCode === 2000) {
        this.setToken(res.respData.token);
        this.setIsGuest(false);
        await this.fetchUserInfo();

        // 登录成功后获取菜单数据
        const menuStore = useMenuStore();
        await menuStore.refreshMenuList(false);

        return Promise.resolve(res.respData);
      } else {
        ElMessage.error(res.respMsg);
        return Promise.reject();
      }
    },

    // 访客登录
    async guestLogin(): Promise<LoginResult> {
      const res = await api_guestLogin();
      if (res.respCode === 2000) {
        this.setToken(res.respData.token);
        this.setIsGuest(true);
        this.setUserInfo({
          username: "访客用户",
          userType: res.respData.userType
        });

        // 访客登录成功后获取访客菜单数据
        const menuStore = useMenuStore();
        await menuStore.refreshMenuList(true);

        return Promise.resolve(res.respData);
      } else {
        ElMessage.error(res.respMsg);
        return Promise.reject();
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      const res = await api_getUserInfo();
      if (res.respCode === 2000) {
        this.setUserInfo(res.respData);
        return Promise.resolve(res.respData);
      } else {
        ElMessage.error(res.respMsg);
        return Promise.reject();
      }
    },

    // 用户登出
    async logout() {
      try {
        await api_logout();
      } catch (error) {
        console.error("登出接口调用失败:", error);
        // 即使接口失败也要清除本地状态
      } finally {
        this.clearUserInfo();
        ElMessage.success("登出成功");
        await router.push(LOGIN_URL);
      }
    },

    // 清除用户信息
    clearUserInfo() {
      this.token = "";
      this.userInfo = {};
      this.roles = [];
      this.isGuest = false;

      // 同时清除菜单数据
      const menuStore = useMenuStore();
      menuStore.clearMenuList();
    },

    // 重置用户信息（路由守卫中使用的别名）
    resetUserInfo() {
      this.clearUserInfo();
    },

    // 初始化用户状态（应用启动时调用）
    async initUserState() {
      // 简单的初始化，不进行自动登录
      console.log("用户状态初始化完成");
    }
  },

  // 持久化配置
  persist: userAuthPersist
});
