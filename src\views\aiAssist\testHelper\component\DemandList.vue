<template>
  <div class="container">
    <el-card style="height: 100%; min-height: 300px">
      <!--页头-->
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleReturn" size="small" icon="ArrowLeft">返回列表</el-button>
          <span class="title">功能点详情</span>
        </div>
      </template>

      <!--功能点内容-->
      <div class="demand-content">
        <!--左侧树区域-->
        <div class="tree-container">
          <div class="tree-header">
            <div class="tree-header-actions">
              <div class="batch-actions">
                <el-button
                  type="primary"
                  size="small"
                  :loading="generating"
                  :disabled="!hasCheckedLeafNodes"
                  @click="handleGenerateTestCases"
                  icon="Plus"
                >
                  生成测试案例
                </el-button>

                <el-button
                  type="primary"
                  size="small"
                  :loading="downloading"
                  :disabled="!hasCheckedLeafNodes"
                  @click="handleDownloadTestCases"
                  icon="Download"
                >
                  下载测试案例
                </el-button>
              </div>

              <div class="batch-actions">
                <el-button
                  type="danger"
                  size="small"
                  :loading="deleting"
                  :disabled="!hasCheckedLeafNodes"
                  @click="handleDeleteFunctionPoints"
                  icon="Delete"
                >
                  删除功能点
                </el-button>
              </div>

              <div class="test-case-toggle">
                <el-switch v-model="directTestCaseMode" active-text="优先测试案例" inactive-text="优先需求详情" size="small" />
              </div>
            </div>
          </div>
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            highlight-current
            show-checkbox
            default-expand-all
            :expand-on-click-node="false"
            :check-on-click-leaf="false"
            @node-click="handleNodeClick"
            @check="handleCheck"
            class="custom-tree"
          />
        </div>

        <!--右侧内容区域-->
        <component
          :is="currentComponent"
          :selected-node="selectedNode"
          @switch-component="handleSwitchComponent"
        />
      </div>
    </el-card>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="确认删除" width="30%" :close-on-click-modal="false">
      <span>确定要删除选中的功能点吗？此操作不可恢复。</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete" :loading="deleting">确认删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, markRaw, watch, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  api_getDetailById,
  api_generateTestCase,
  TreeNode,
  api_deleteFunctionPointsById
} from "@/api/modules/service-llm/demand-doc";
import { SERVICE_LLM } from "@/api/config/servicePort";
import { ElMessage } from "element-plus";
import DemandDetail from "./DemandDetail.vue";
import TestCaseList from "./TestCaseList.vue";
import TestCaseReview from "./TestCaseReview.vue";
import axios from "axios";

const route = useRoute();
const router = useRouter();
const demandDetail = ref();
const treeData = ref<TreeNode[]>([]);
const selectedNode = ref<TreeNode | null>(null);
const currentComponent = ref<any>(markRaw(DemandDetail));
const treeRef = ref();
const checkedNodes = ref<string[]>([]);
const generating = ref(false);
const downloading = ref(false);
const directTestCaseMode = ref(false);
const deleting = ref(false);
const deleteDialogVisible = ref(false);
const fileId = ref<string>("");

const components: Record<string, any> = {
  DemandDetail: markRaw(DemandDetail),
  TestCaseList: markRaw(TestCaseList),
  TestCaseReview: markRaw(TestCaseReview)
};

const defaultProps = {
  children: "children",
  label: "title"
};

// 处理节点点击事件
const handleNodeClick = (node: TreeNode) => {
  selectedNode.value = node;

  // 根据切换开关状态决定显示需求详情还是直接显示测试案例
  if (directTestCaseMode.value && isLeafNode(node)) {
    currentComponent.value = components.TestCaseList;
  } else {
    currentComponent.value = components.DemandDetail;
  }
};

// 处理节点复选框选中事件
const handleCheck = (node: TreeNode, checked: { checkedKeys: string[]; checkedNodes: TreeNode[] }) => {
  checkedNodes.value = checked.checkedKeys;
};

// 递归为每个节点添加唯一id
const processTreeData = (nodes: any[], parentId = ""): TreeNode[] => {
  if (!nodes) return [];

  return nodes.map((node, index) => {
    const currentId = parentId ? `${parentId}-${index}` : `${index}`;
    const processedNode: TreeNode = {
      ...node,
      id: currentId
    };

    if (node.children && node.children.length > 0) {
      processedNode.children = processTreeData(node.children, currentId);
    }

    return processedNode;
  });
};

// 返回文件列表页面
const handleReturn = () => {
  router.back();
};

// 处理组件切换
const handleSwitchComponent = (componentName: string) => {
  currentComponent.value = components[componentName];
};

// 监听 selectedNode 变化
watch(
  () => selectedNode.value,
  () => {
    // 只有在非直接测试案例模式下才重置为需求详情
    if (!directTestCaseMode.value) {
      currentComponent.value = components.DemandDetail;
    } else if (selectedNode.value && isLeafNode(selectedNode.value)) {
      // 在直接测试案例模式下，如果是叶子节点，则显示测试案例
      currentComponent.value = components.TestCaseList;
    }
  }
);

// 获取所有选中的叶子节点的 functionId
const getCheckedLeafNodeIds = () => {
  const checkedNodes = treeRef.value?.getCheckedNodes() || [];
  return checkedNodes
    .filter((node: TreeNode) => isLeafNode(node))
    .map((node: TreeNode) => node.function_id)
    .filter(Boolean) as string[];
};

// 判断是否有选中的叶子节点
const hasCheckedLeafNodes = computed(() => {
  return getCheckedLeafNodeIds().length > 0;
});

// 处理生成测试案例
const handleGenerateTestCases = async () => {
  const functionIds = getCheckedLeafNodeIds();
  if (functionIds.length === 0) {
    ElMessage.warning("请选择至少一个功能点");
    return;
  }

  generating.value = true;
  api_generateTestCase(functionIds)
    .then(res => {
      // 使用类型断言确保可以访问respCode和respMsg
      const result = res as { respCode: number; respMsg: string };
      if (result.respCode === 2000) {
        ElMessage.success("测试案例生成任务已提交");
      } else {
        ElMessage.error(result.respMsg || "生成失败");
      }
    })
    .finally(() => {
      generating.value = false;
    });
};

// 处理下载测试案例
const handleDownloadTestCases = async () => {
  const functionIds = getCheckedLeafNodeIds();
  if (functionIds.length === 0) {
    ElMessage.warning("请选择至少一个功能点");
    return;
  }

  downloading.value = true;
  try {
    // 使用axios直接请求以获取完整响应，包括headers
    const baseURL = import.meta.env.VITE_API_URL;
    const url = `${baseURL}${SERVICE_LLM}/demandDoc/testcaseDownload`;
    const response = await axios.post(url, functionIds, {
      responseType: "blob",
      headers: {
        "Content-Type": "application/json"
      }
    });

    // 从Content-Disposition获取文件名，如果没有则使用默认名称
    let filename = "testcases.csv";
    const contentDisposition = response.headers["content-disposition"];
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1];
      }
    }

    // 下载文件
    const blob = new Blob([response.data], { type: "text/csv" });
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    ElMessage.success("测试案例下载成功");
  } catch (error) {
    ElMessage.error("下载测试案例失败");
  } finally {
    downloading.value = false;
  }
};

// 判断是否为叶子节点（第三层节点）
const isLeafNode = (node: TreeNode) => {
  return node.function_id && (!node.children || node.children.length === 0);
};

// 处理删除功能点
const handleDeleteFunctionPoints = () => {
  const functionIds = getCheckedLeafNodeIds();
  if (functionIds.length === 0) {
    ElMessage.warning("请选择至少一个功能点");
    return;
  }

  deleteDialogVisible.value = true;
};

// 确认删除功能点
const confirmDelete = () => {
  const functionIds = getCheckedLeafNodeIds();

  deleting.value = true;
  api_deleteFunctionPointsById(functionIds)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("功能点删除成功");
        // 关闭对话框
        deleteDialogVisible.value = false;
        // 重新加载树数据
        fetchDetailByFileId(fileId.value);
      } else {
        ElMessage.error(res.respMsg || "删除失败");
      }
    })
    .finally(() => {
      deleting.value = false;
    });
};

const fetchDetailByFileId = function (fileId: string) {
  api_getDetailById(fileId).then(res => {
    if (res.respCode === 2000) {
      demandDetail.value = res.respData;
      treeData.value = processTreeData(res.respData);
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

onMounted(() => {
  const id = route.params.id;
  fileId.value = Array.isArray(id) ? id[0] : id;
  fetchDetailByFileId(fileId.value);
});
</script>

<style lang="scss" scoped>
.container {
  height: 100%;

  .card-header {
    display: flex;
    align-items: center;

    .title {
      margin-left: 20px;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .demand-content {
    display: flex;
    height: calc(100vh - 200px);
    margin-top: 5px;

    .tree-container {
      flex: 0 0 280px;
      overflow-y: auto;
      border-right: 1px solid #e6e6e6;
      padding-right: 15px;
      height: 100%;

      .tree-header {
        margin-bottom: 15px;
        padding: 0 0 10px;
        border-bottom: 1px solid #ebeef5;

        .tree-header-actions {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .batch-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;

            .el-button {
              flex: 1;
              min-width: 120px;
              justify-content: center;
            }
          }

          .test-case-toggle {
            margin-top: 2px;
            display: flex;
            justify-content: center;
            padding-top: 8px;
            border-top: 1px dashed #ebeef5;
          }
        }
      }

      .custom-tree {
        :deep(.el-tree-node__label) {
          white-space: normal;
          word-break: break-word;
          line-height: 1.4;
        }

        :deep(.el-tree-node__content) {
          height: auto;
          min-height: 26px;
          padding: 4px 0;
        }
      }
    }
  }
}
</style>
