import { createRouter, createWebHashHistory, createWebHistory } from "vue-router";
import { staticRouter, errorRouter } from "@/routers/modules/staticRouter";
import NProgress from "@/config/nprogress";
import { useUserStore } from "@/stores/modules/user";
import { useMenuStore } from "@/stores/modules/menu";
import { LOGIN_URL } from "@/config";
import { ElMessage } from "element-plus";

const mode = import.meta.env.VITE_ROUTER_MODE;

const routerMode = {
  hash: () => createWebHashHistory(),
  history: () => createWebHistory()
};

/**
 * @description 📚 路由参数配置简介
 * @param path ==> 路由菜单访问路径
 * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
 * @param redirect ==> 路由重定向地址
 * @param component ==> 视图文件路径
 * @param meta ==> 路由菜单元信息
 * @param meta.icon ==> 菜单和面包屑对应的图标
 * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
 * @param meta.activeMenu ==> 当前路由为详情页时，需要高亮的菜单
 * @param meta.isLink ==> 路由外链时填写的访问地址
 * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
 * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
 * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
 * @param meta.isKeepAlive ==> 当前路由是否缓存
 * */
const router = createRouter({
  history: routerMode[mode](),
  routes: [...staticRouter, ...errorRouter],
  strict: false,
  scrollBehavior: () => ({ left: 0, top: 0 })
});

/**
 * @description 路由拦截 beforeEach
 * */
router.beforeEach(async (to, from, next) => {
  // 1.NProgress 开始
  NProgress.start();

  // 2.动态设置标题
  const title = import.meta.env.VITE_GLOB_APP_TITLE;
  document.title = to.meta.title ? `${to.meta.title} - ${title}` : title;

  // 3.判断是访问登陆页，有Token就在当前页面，没有Token就去登陆页
  if (to.path.toLocaleLowerCase() === LOGIN_URL) {
    const userStore = useUserStore();
    if (userStore.getToken) return next(from.fullPath);
    return next();
  }

  // 4.判断访问页面是否在路由白名单地址(静态路由)中，如果存在直接放行
  const whiteList = ["/403", "/404", "/500"];
  if (whiteList.includes(to.path)) return next();

  // 5.判断是否有Token，没有重定向到login页面
  const userStore = useUserStore();
  if (!userStore.getToken) {
    ElMessage.warning("您还未登录，请先登录！");
    return next({ path: LOGIN_URL, replace: true });
  }

  // 6.如果没有用户信息，重新获取用户信息
  const currentUserInfo = userStore.getUserInfo;
  if (!currentUserInfo || !currentUserInfo.username) {
    try {
      await userStore.fetchUserInfo();
    } catch (error) {
      // 获取用户信息失败，可能token已过期
      userStore.resetUserInfo();
      ElMessage.error("登录信息已过期，请重新登录！");
      return next({ path: LOGIN_URL, replace: true });
    }
  }

  // 7.检查并更新菜单数据
  const menuStore = useMenuStore();
  // 检查菜单数据是否需要更新
  const menuUpdated = await menuStore.checkAndUpdateMenu(userStore.getIsGuest);

  // 如果菜单数据更新了，需要重新注册动态路由
  if (menuUpdated && !menuStore.isRoutesRegistered) {
    menuStore.registerDynamicRoutes(router);
  }

  // 正常访问页面
  next();
});

/**
 * @description 路由跳转错误
 * */
router.onError(error => {
  NProgress.done();
  console.warn("路由错误", error.message);
});

/**
 * @description 路由跳转结束
 * */
router.afterEach(() => {
  NProgress.done();
});

export default router;
