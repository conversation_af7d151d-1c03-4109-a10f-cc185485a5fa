import http from "@/api";
import { SERVICE_ENR, SERVICE_SERVER_OPERATION } from "@/api/config/servicePort";
import { Page } from "@/api/interface";

// 探针管理相关接口
export interface EnrAgentServer {
  serverInfoId: string;
  agentStatus: number;
}

export interface EnrAgentServerDto extends EnrAgentServer {
  id: string;
  serverName: string;
  serverIp: string;
  username: string;
  pw: string;
  environment: string;
  authType: string;
  remark: string;
  addTime: string;
  checking?: boolean;
  deploying?: boolean;
}

export interface agentDeployDto {
  serverAddress: string;
  agentName: string;
  port: number;
  description?: string;
}

// 请求重发相关接口
export interface ResendTaskDto {
  dataSource: string;
  interfaceId?: string;
  startTime: number;
  endTime: number;
  targetUrl: string;
  timeout: number;
  threadCount: number;
  interval: number;
  headers?: string;
  taskName: string;
  filter?: string;
  ignoreError: boolean;
}

export interface ResendTask {
  id: string;
  taskName: string;
  dataSource: string;
  interfaceId?: string;
  targetUrl: string;
  startTime: number;
  endTime: number;
  status: "CREATED" | "RUNNING" | "COMPLETED" | "FAILED";
  createTime: string;
  completedTime?: string;
  totalCount?: number;
  successCount?: number;
  failCount?: number;
}

// 探针管理API
export const api_fetchAgentList = (pageNo: number, pageSize: number, enrAgentServerDto: Partial<EnrAgentServerDto>) => {
  return http.post<Page<EnrAgentServerDto[]>>(
    `${SERVICE_SERVER_OPERATION}/enr/agent/list?pageNo=${pageNo}&pageSize=${pageSize}`,
    enrAgentServerDto
  );
};

export const api_startRecord = (serverInfoId: string, deviceName: string, port: string) => {
  return http.get(`${SERVICE_SERVER_OPERATION}/enr/agent/startRecord/${serverInfoId}?deviceName=${deviceName}&port=${port}`);
};

export const api_stopRecord = (agentId: string) => {
  return http.get(`${SERVICE_SERVER_OPERATION}/enr/agent/stopRecord/${agentId}`);
};

// 检查探针状态
export const api_checkAgentStatus = (serverInfoId: string) => {
  return http.get<number>(SERVICE_SERVER_OPERATION + "/enr/agent/checkStatus", { serverInfoId });
};

// 部署探针脚本
export const api_deployAgentScript = (serverInfoId: string) => {
  return http.get(SERVICE_SERVER_OPERATION + "/enr/agent/setup", { serverInfoId });
};

// 新增服务器为流量回放服务器API
export const api_addAgentServer = (serverInfoId: string) => {
  return http.get(`${SERVICE_SERVER_OPERATION}/enr/agent/addAgentServer`, { id: serverInfoId });
};

// 请求重发API
export const api_submitResendTask = (resendTaskDto: ResendTaskDto) => {
  return http.post<string>(SERVICE_ENR + "/config/resend/submit", resendTaskDto);
};

export const api_fetchResendTasks = () => {
  return http.get<ResendTask[]>(SERVICE_ENR + "/config/resend/list");
};

export const api_getResendTaskDetail = (taskId: string) => {
  return http.get<ResendTask>(SERVICE_ENR + "/config/resend/detail", { taskId });
};

export const api_cancelResendTask = (taskId: string) => {
  return http.get(SERVICE_ENR + "/config/resend/cancel", { taskId });
};
