import { defineStore } from "pinia";
import { ElMessage } from "element-plus";
import { api_getUserMenus, api_getGuestMenus, MenuItem } from "@/api/modules/service-auth/auth";
import { createPersistConfig } from "@/stores/config/persist";

// 菜单状态接口
export interface MenuState {
  menuList: any[];
  lastUpdateTime: number;
  cacheExpireTime: number; // 缓存过期时间（毫秒）
  routesRegistered: boolean; // 动态路由是否已注册
  rawMenuData: MenuItem[]; // 保存原始菜单数据，用于刷新后重新生成组件函数
}

export const useMenuStore = defineStore("menu", {
  state: (): MenuState => ({
    menuList: [],
    lastUpdateTime: 0,
    cacheExpireTime: 30 * 60 * 1000, // 30分钟过期时间
    routesRegistered: false, // 动态路由是否已注册
    rawMenuData: [] // 保存原始菜单数据
  }),

  getters: {
    // 获取菜单列表
    getMenuList: state => state.menuList,

    // 检查菜单数据是否过期
    isMenuExpired: state => {
      const now = Date.now();
      return now - state.lastUpdateTime > state.cacheExpireTime;
    },

    // 检查是否有菜单数据
    hasMenuData: state => state.menuList.length > 0,

    // 获取菜单更新时间
    getLastUpdateTime: state => state.lastUpdateTime,

    // 检查动态路由是否已注册
    isRoutesRegistered: state => state.routesRegistered
  },

  actions: {
    // 设置菜单列表
    setMenuList(menuList: any[], rawMenuData?: MenuItem[]) {
      this.menuList = menuList;
      this.lastUpdateTime = Date.now();
      if (rawMenuData) {
        this.rawMenuData = rawMenuData;
      }
    },

    // 清空菜单数据
    clearMenuList() {
      this.menuList = [];
      this.lastUpdateTime = 0;
      this.routesRegistered = false;
      this.rawMenuData = [];
    },

    // 设置路由注册状态
    setRoutesRegistered(registered: boolean) {
      this.routesRegistered = registered;
    },

    // 从后端获取菜单数据
    async fetchMenuList(isGuest: boolean = false): Promise<boolean> {
      try {
        let apiMenus: MenuItem[] = [];

        if (isGuest) {
          // 访客用户，获取访客菜单
          const res = await api_getGuestMenus();
          apiMenus = res.respData || [];
        } else {
          // 普通用户，获取用户菜单
          const res = await api_getUserMenus();
          apiMenus = res.respData || [];
        }

        // 将后端菜单数据转换为前端路由格式
        const convertedMenus = this.convertApiMenusToRoutes(apiMenus);
        this.setMenuList(convertedMenus, apiMenus);
        return true;
      } catch (error) {
        ElMessage.warning("菜单加载失败，请刷新页面重试");
        return false;
      }
    },

    // 检查并更新菜单数据
    async checkAndUpdateMenu(isGuest: boolean = false): Promise<boolean> {
      // 如果没有菜单数据或数据已过期，则重新获取
      if (!this.hasMenuData || this.isMenuExpired) {
        return await this.fetchMenuList(isGuest);
      }

      return true;
    },

    // 强制刷新菜单数据
    async refreshMenuList(isGuest: boolean = false): Promise<boolean> {
      return await this.fetchMenuList(isGuest);
    },

    // 将后端菜单数据转换为前端路由格式
    convertApiMenusToRoutes(apiMenus: MenuItem[]): any[] {
      return apiMenus.map(menu => {
        // 处理组件路径
        let component: (() => Promise<any>) | undefined = undefined;
        if (menu.componentPath) {
          component = this.createComponentFunction(menu.componentPath);
        }

        return {
          path: menu.menuPath,
          name: menu.menuName,
          component: component,
          redirect: menu.redirectPath,
          componentPath: menu.componentPath, // 保存原始组件路径
          meta: {
            icon: menu.menuIcon || "",
            title: menu.menuTitle,
            isLink: menu.isLink || "",
            isHide: menu.isHide === 1,
            isFull: menu.isFull === 1,
            isAffix: menu.isAffix === 1,
            isKeepAlive: menu.isKeepAlive === 1,
            allowGuest: menu.allowGuest === 1,
            requiredRoles: menu.requiredRoles ? JSON.parse(menu.requiredRoles) : []
          },
          children: menu.children ? this.convertApiMenusToRoutes(menu.children) : undefined
        };
      });
    },

    // 创建组件函数
    createComponentFunction(componentPath: string): (() => Promise<any>) | undefined {
      // 使用动态导入模块映射表
      const modules = import.meta.glob("@/views/**/*.vue");
      const normalizedPath = `/src/views/${componentPath.replace("@/views/", "").replace(/^\//, "")}`;

      if (modules[normalizedPath]) {
        return modules[normalizedPath];
      } else {
        console.warn(`组件路径不存在: ${normalizedPath}`);
        return undefined;
      }
    },

    // 重新生成菜单列表的组件函数（用于刷新后恢复）
    regenerateComponentFunctions() {
      if (this.rawMenuData.length === 0) {
        return;
      }

      // 重新转换菜单数据，生成新的组件函数
      const regeneratedMenus = this.convertApiMenusToRoutes(this.rawMenuData);
      this.menuList = regeneratedMenus;

      // 重置路由注册状态，确保路由会被重新注册
      this.routesRegistered = false;
    },

    // 注册动态路由到路由器
    registerDynamicRoutes(router: any) {
      if (this.routesRegistered || this.menuList.length === 0) {
        console.log("动态路由已注册或菜单数据为空，跳过注册");
        return;
      }

      // 获取 layout 路由
      const layoutRoute = router.getRoutes().find((route: any) => route.name === "layout");
      console.log("layoutRoute:", layoutRoute);
      if (!layoutRoute) {
        console.error("未找到 layout 路由");
        return;
      }

      // 扁平化菜单列表，获取所有路由
      const flatRoutes = this.getFlatRoutes(this.menuList);
      // 逐个添加动态路由
      flatRoutes.forEach((route: any) => {
        if (route.component && route.path !== "/home/<USER>") {
          // 跳过首页，因为已经在静态路由中
          try {
            // 检查路由是否已存在
            const existingRoute = router.getRoutes().find((r: any) => r.path === route.path);
            if (!existingRoute) {
              router.addRoute("layout", route);
            }
          } catch (error) {
            console.warn(`添加动态路由失败: ${route.path}`, error);
          }
        }
      });

      this.setRoutesRegistered(true);
    },

    // 扁平化路由列表
    getFlatRoutes(routes: any[]): any[] {
      const flatRoutes: any[] = [];

      const flatten = (routeList: any[]) => {
        routeList.forEach(route => {
          // 添加当前路由
          flatRoutes.push({
            path: route.path,
            name: route.name,
            component: route.component,
            redirect: route.redirect,
            meta: route.meta
          });

          // 递归处理子路由
          if (route.children && route.children.length > 0) {
            flatten(route.children);
          }
        });
      };

      flatten(routes);
      return flatRoutes;
    },

    // 检查菜单数据是否过期并清理
    checkAndClearExpiredMenu() {
      if (this.isMenuExpired) {
        this.clearMenuList();
      }
    },

    // 初始化菜单状态
    initMenuState() {
      // 检查并清理过期的菜单数据
      this.checkAndClearExpiredMenu();

      // 如果有原始菜单数据但没有转换后的菜单列表，重新生成
      if (this.rawMenuData.length > 0 && this.menuList.length === 0) {
        this.regenerateComponentFunctions();
      }

      // 重置路由注册状态，确保在应用启动时能重新注册路由
      if (this.hasMenuData) {
        this.routesRegistered = false;
      }

      console.log("初始化菜单状态完成");
    }
  },

  // 持久化配置 - 只存储原始数据，恢复时重新生成menuList
  persist: createPersistConfig(
    "menu-cache",
    sessionStorage,
    ["lastUpdateTime", "rawMenuData"], // 移除routesRegistered，每次启动都重新注册
    undefined, // beforeRestore
    (context: any) => {
      // afterRestore: 恢复数据后重新生成menuList
      const store = context.store;

      // 如果有原始菜单数据，重新生成menuList
      if (store.rawMenuData.length > 0) {
        store.regenerateComponentFunctions();
      }

      // 重置路由注册状态，确保每次启动都重新注册路由
      store.routesRegistered = false;
    }
  )
});
