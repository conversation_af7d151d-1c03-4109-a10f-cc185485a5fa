<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <Fullscreen id="fullscreen" />
    </div>
    <span class="username">{{ displayUsername }}</span>
    <el-dropdown @command="handleCommand" trigger="click">
      <div class="avatar-dropdown">
        <Avatar />
        <el-icon class="el-icon--right">
          <arrow-down />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="userInfo">
            <el-icon><User /></el-icon>
            个人信息
          </el-dropdown-item>
          <el-dropdown-item command="changePassword" :disabled="userStore.getIsGuest">
            <el-icon><Lock /></el-icon>
            修改密码
          </el-dropdown-item>
          <el-dropdown-item divided command="logout">
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { ArrowDown, User, Lock, SwitchButton } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/modules/user";
import Fullscreen from "./components/Fullscreen.vue";
import Avatar from "./components/Avatar.vue";

const userStore = useUserStore();

// 显示用户名
const displayUsername = computed(() => {
  const userInfo = userStore.getUserInfo;
  if (userStore.getIsGuest) {
    return "访客用户";
  }
  return userInfo?.username || "未知用户";
});

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case "userInfo":
      // 显示用户信息对话框
      ElMessage.info("个人信息功能待开发");
      break;
    case "changePassword":
      // 显示修改密码对话框
      ElMessage.info("修改密码功能待开发");
      break;
    case "logout":
      // 确认登出
      ElMessageBox.confirm("确定要退出登录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        userStore.logout();
      }).catch(() => {
        // 用户取消操作
      });
      break;
  }
};
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 25px;
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);
    }
  }
  .username {
    margin: 0 20px;
    font-size: 15px;
    color: var(--el-header-text-color);
  }
  .avatar-dropdown {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    .el-icon {
      margin-left: 5px;
      color: var(--el-header-text-color);
    }
  }
}
</style>
