<template>
  <div class="container">
    <div class="card">
      <h4 class="title">ESB流量回放配置</h4>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="探针服务器管理" name="agentManagement">
          <AgentManagement />
        </el-tab-pane>
        <el-tab-pane label="请求重发配置" name="requestResend">
          <RequestResend />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import AgentManagement from "@/views/esbNetworkReplay/configManagement/components/AgentManagement.vue";
import RequestResend from "@/views/esbNetworkReplay/configManagement/components/RequestResend.vue";

const activeTab = ref("agentManagement");
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  box-sizing: border-box;
  
  .title {
    margin: 0 0 20px;
    font-size: 18px;
    font-weight: bold;
    color: var(--el-color-info-dark-2);
  }
}
</style>
