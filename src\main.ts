import { createApp } from "vue";
import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS enrCommon style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";
// font css
import "@/assets/fonts/font.scss";
// element css
import "element-plus/dist/index.css";
// custom element css
import "@/styles/element.scss";
// svg icons
import "virtual:svg-icons-register";
// element plus
import ElementPlus from "element-plus";
// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
// import directives from "@/directives/index";
// vue Router
import router from "@/routers";
// pinia store
import pinia from "@/stores";
// errorHandler
import errorHandler from "@/utils/errorHandler";
// user store for initialization
import { useUserStore } from "@/stores/modules/user";
import { useMenuStore } from "@/stores/modules/menu";

const app = createApp(App);

app.config.errorHandler = errorHandler;

// register the element Icons component
Object.keys(Icons).forEach(key => {
  app.component(key, Icons[key as keyof typeof Icons]);
});

app.use(ElementPlus).use(pinia);

// 初始化应用的异步函数
async function initializeApp() {
  // 初始化用户状态和菜单状态
  const userStore = useUserStore();
  const menuStore = useMenuStore();

  // 等待状态初始化完成
  await Promise.all([
    userStore.initUserState(),
    menuStore.initMenuState()
  ]);

  // 等待一个微任务，确保Pinia的afterRestore回调已经执行
  await new Promise(resolve => setTimeout(resolve, 0));

  // 如果有有效的菜单数据且路由未注册，则在挂载路由器之前注册动态路由
  if (menuStore.hasMenuData && !menuStore.isRoutesRegistered) {
    console.log("应用启动时注册动态路由");
    menuStore.registerDynamicRoutes(router);
  }

  // 在动态路由注册完成后再挂载路由器
  app.use(router);

  // 最后挂载应用
  app.mount("#app");
}

// 启动应用
initializeApp().catch(error => {
  console.error("应用初始化失败:", error);
  // 即使初始化失败也要挂载应用，避免白屏
  app.use(router);
  app.mount("#app");
});
