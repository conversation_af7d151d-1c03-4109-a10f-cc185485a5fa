import { RouteRecordRaw } from "vue-router";

/**
 * 菜单权限过滤工具
 */

// 权限检查函数（简化版，仅支持角色和访客权限）
export const hasPermission = (
  requiredRoles: string[] = [],
  userRoles: string[] = [],
  isGuest: boolean = false,
  allowGuest: boolean = false
): boolean => {
  // 如果是访客且不允许访客访问
  if (isGuest && !allowGuest) {
    return false;
  }

  // 如果是访客且允许访客访问
  if (isGuest && allowGuest) {
    return true;
  }

  // 检查角色权限
  if (requiredRoles.length > 0) {
    const hasRole = requiredRoles.some(role => userRoles.includes(role));
    if (!hasRole) return false;
  }

  return true;
};

// 过滤菜单（简化版，仅基于角色和访客权限）
export const filterMenuByPermission = (
  menuList: RouteRecordRaw[],
  userRoles: string[] = [],
  isGuest: boolean = false
): RouteRecordRaw[] => {
  return menuList.filter(menu => {
    const meta = menu.meta as any;

    // 检查当前菜单权限（仅基于角色和访客权限）
    const canAccess = hasPermission(
      meta?.requiredRoles || [],
      userRoles,
      isGuest,
      meta?.allowGuest || false
    );

    if (!canAccess) {
      return false;
    }

    // 递归过滤子菜单
    if (menu.children && menu.children.length > 0) {
      menu.children = filterMenuByPermission(
        menu.children,
        userRoles,
        isGuest
      );

      // 如果所有子菜单都被过滤掉了，则隐藏父菜单
      if (menu.children.length === 0) {
        return false;
      }
    }

    return true;
  });
};

// 获取访客可访问的菜单
export const getGuestMenuList = (menuList: RouteRecordRaw[]): RouteRecordRaw[] => {
  return filterMenuByPermission(menuList, [], true);
};

// 获取用户可访问的菜单（简化版）
export const getUserMenuList = (
  menuList: RouteRecordRaw[],
  userRoles: string[]
): RouteRecordRaw[] => {
  return filterMenuByPermission(menuList, userRoles, false);
};
