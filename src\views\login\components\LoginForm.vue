<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="username">
      <el-input v-model="loginForm.username" placeholder="用户名">
        <template #prefix>
          <el-icon class="el-input__icon">
            <user />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input v-model="loginForm.password" type="password" placeholder="密码" show-password autocomplete="new-password">
        <template #prefix>
          <el-icon class="el-input__icon">
            <Lock />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>

  </el-form>
  <div class="login-btn">
    <el-button :icon="CircleClose" round size="large" @click="resetForm(loginFormRef)"> 重置 </el-button>
    <el-button :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login(loginFormRef)">
      登录
    </el-button>
    <el-button :icon="User" round size="large" type="success" :loading="guestLoading" @click="guestLogin">
      访客登录
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { HOME_URL } from "@/config";
import { getTimeState } from "@/utils";
import { ElNotification, ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { CircleClose, UserFilled, User, Lock } from "@element-plus/icons-vue";
import type { ElForm } from "element-plus";
import { LoginForm } from "@/api/modules/service-auth/auth";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, max: 20, message: "用户名长度在 2 到 20 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
  ]
});

const loading = ref(false);
const guestLoading = ref(false);
const loginForm = reactive<LoginForm>({
  username: "",
  password: ""
});

// 普通用户登录
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行登录接口
      await userStore.login(loginForm);

      // 2.跳转到目标页面或首页
      const redirect = (route.query.redirect as string) || HOME_URL;
      await router.push(redirect);
      ElNotification({
        title: getTimeState(),
        message: "欢迎登录",
        type: "success",
        duration: 3000
      });
    } catch (error) {
      console.error("登录失败:", error);
    } finally {
      loading.value = false;
    }
  });
};

// 访客登录
const guestLogin = async () => {
  guestLoading.value = true;
  try {
    // 1.执行访客登录接口（已包含菜单更新）
    await userStore.guestLogin();

    // 2.跳转到目标页面或首页
    const redirect = (route.query.redirect as string) || HOME_URL;
    await router.push(redirect);
    ElNotification({
      title: getTimeState(),
      message: "欢迎访客用户",
      type: "success",
      duration: 3000
    });
  } catch (error) {
    console.error("访客登录失败:", error);
    ElMessage.error("访客登录失败，请稍后重试");
  } finally {
    guestLoading.value = false;
  }
};

// resetForm
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value || guestLoading.value) return;
      login(loginFormRef.value);
    }
  };
});
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
