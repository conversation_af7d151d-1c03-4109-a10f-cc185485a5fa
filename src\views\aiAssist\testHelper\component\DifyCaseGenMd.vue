<template>
  <div class="dify-case-gen-container">
    <el-card style="height: 100%; min-height: 400px">
      <template #header>
        <div class="card-header">
          <el-button 
            type="primary" 
            :icon="ArrowLeft" 
            @click="goBack"
            class="back-button"
          >
            返回
          </el-button>
          <span class="title">智能案例生成</span>
        </div>
      </template>
      
      <div 
        class="iframe-container"
        v-loading="loading"
        element-loading-text="正在加载"
        element-loading-background="rgba(255, 255, 255, 0.9)"
      >
        <iframe
          :src="iframeSrc"
          class="dify-iframe"
          allowfullscreen
          @load="handleIframeLoad"
        >
          您的浏览器不支持 iframe，请升级浏览器或使用其他浏览器访问。
        </iframe>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(true)

// iframe 源地址
const iframeSrc = 'http://**************:8888/workflow/afY22Zuy6kp3PI4Y'

// 返回上一页
const goBack = () => {
  router.back()
}

// iframe 加载完成处理
const handleIframeLoad = () => {
  loading.value = false
}
</script>

<style lang="scss" scoped>
.dify-case-gen-container {
  height: 100%;

  :deep(.el-card__body) {
    height: calc(100% - 110px);
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border-radius: 6px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.iframe-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.dify-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: #fff;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
