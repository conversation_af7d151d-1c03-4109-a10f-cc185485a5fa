<template>
  <div class="container">

    <div class="card">
      <el-form>
        <el-form-item>
          <el-button size="small" @click="handleReturn">返回</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="margin-top: 0.5%">
      <el-table border :data="infoDetailTableData.data" highlight-current-row>
        <el-table-column prop="id" label="ID" fixed="left" width="300"/>
        <el-table-column prop="csmrId" label="请求方ID" width="100"/>
        <el-table-column prop="versionNumber" label="版本号"/>
        <el-table-column prop="transCde" label="交易代码" width="100"/>
        <el-table-column prop="channelId" label="渠道ID"/>
        <el-table-column prop="esbRespCode" label="响应码" show-overflow-tooltip width="200"/>
        <el-table-column prop="esbRespMsg" label="响应信息" show-overflow-tooltip width="200"/>
        <el-table-column prop="requestTime" label="请求时间" width="200"/>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="queryRequestBody(scope.row.id)">请求报文</el-button>
            <el-button type="primary" size="small" link @click="queryResponseBody(scope.row.id)">响应报文</el-button>
            <el-button type="danger" size="small" link @click="deleteAsset(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="infoDetailTableData.pageNo"
        :page-size="infoDetailTableData.pageSize"
        :total="infoDetailTableData.totalCount"
        :page-count="infoDetailTableData.pageCount"
        layout="prev, pager, next, jumper"
        @current-change="pageChange"
      />

      <el-dialog v-model="dialogData.visible" :title="dialogData.title">
        <pre v-text="dialogData.content"></pre>
      </el-dialog>
    </div>

  </div>
</template>

<script setup lang="ts">
import {
  api_deleteAsset,
  api_getAssetDetailFieldValue,
  api_getPagedAssetInfoDetail, AssetInfo,
  AssetInfoDetailSearchDto,
} from "@/api/modules/esbNetworkReplay/asset";
import {onBeforeMount, ref} from "vue";
import vkbeautify from "vkbeautify";
import {ElMessage} from "element-plus";
import {PropsParams} from "@/views/esbNetworkReplay/recordManagement-old/recordInfo/index.vue";

interface InfoDetailTableData {
  data: AssetInfo[];
  pageNo: number;
  pageSize: number;
  totalCount: number;
  pageCount: number;
}

interface DialogData {
  visible: boolean;
  title: string;
  content: string;
}

const emits = defineEmits(["changeComponent"]);
const props = defineProps<{transParams: PropsParams}>();

const searchCondition = ref<AssetInfoDetailSearchDto>({
  pageNo: 1,
  pageSize: 10,
  interfaceId: props.transParams.selectedInterfaceId
});
const infoDetailTableData = ref<InfoDetailTableData>({
  data: [],
  pageNo: 0,
  pageSize: 0,
  totalCount: 0,
  pageCount: 0
});
const dialogData = ref<DialogData>({
  visible: false,
  title: "",
  content: ""
});

const handleReturn = function () {
  emits("changeComponent", "AssetInfo", props.transParams);
};

const queryPagedInfoDetail = function () {
  api_getPagedAssetInfoDetail(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      infoDetailTableData.value.data = res.respData.pageData;
      infoDetailTableData.value.pageNo = res.respData.pageNo;
      infoDetailTableData.value.pageSize = res.respData.pageSize;
      infoDetailTableData.value.totalCount = res.respData.totalCount;
      infoDetailTableData.value.pageCount = res.respData.pageCount;
    }
  })
};

const pageChange = function (pageNo: number) {
  searchCondition.value.pageNo = pageNo;
  queryPagedInfoDetail();
};

const queryRequestBody = function (id: string) {
  dialogData.value.visible = true;
  api_getAssetDetailFieldValue(id, "REQUEST_BODY").then(res => {
    if (res.respCode === 2000) {
      dialogData.value.title = "请求报文";
      dialogData.value.content = vkbeautify.xml(res.respData.requestBody);
    }
  })
};

const queryResponseBody = function (id: string) {
  dialogData.value.visible = true;
  api_getAssetDetailFieldValue(id, "RESPONSE_BODY").then(res => {
    if (res.respCode === 2000) {
      dialogData.value.title = "响应报文";
      dialogData.value.content = vkbeautify.xml(res.respData.responseBody);
    }
  })
};

const deleteAsset = function (id: string) {
  api_deleteAsset(id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
      queryPagedInfoDetail();
    }
  })
};

onBeforeMount(() => {
  queryPagedInfoDetail();
})
</script>

<style scoped lang="scss">

</style>
