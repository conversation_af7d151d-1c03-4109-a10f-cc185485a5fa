/**
 * Pinia持久化配置
 */

import type { PersistedStateOptions } from 'pinia-plugin-persistedstate';

// 持久化配置工厂函数
export const createPersistConfig = (
  key: string,
  storage: Storage = localStorage,
  paths?: string[],
  beforeRestore?: (context: any) => void,
  afterRestore?: (context: any) => void
): PersistedStateOptions => ({
  key,
  storage,
  paths,
  beforeRestore,
  afterRestore,
  serializer: {
    serialize: JSON.stringify,
    deserialize: JSON.parse
  }
});

// 用户认证信息持久化配置
export const userAuthPersist = createPersistConfig(
  'user-auth',
  localStorage,
  ['token', 'isGuest'],
);

// 用户偏好设置持久化配置（暂时保留，可用于其他偏好设置）
export const userPreferencesPersist = createPersistConfig(
  'user-preferences',
  sessionStorage
);
