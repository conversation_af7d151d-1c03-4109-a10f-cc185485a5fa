<template>
  <div class="review-container">
    <!--内容头-->
    <div class="content-header fixed">
      <h3 class="content-title">用例评审 - {{ functionDetail.length > 0 ? functionDetail[0]?.title : "加载中..." }}</h3>
      <div class="action-buttons">
        <el-button @click="switchComponent">返回测试案例</el-button>
      </div>
    </div>

    <!--内容体-->
    <div class="content-body">
      <div class="review-layout">
        <!-- 左侧：功能点详情 -->
        <div class="left-panel">
          <el-card class="function-detail-card">
            <template #header>
              <div class="card-header">
                <span>功能点详情</span>
              </div>
            </template>

            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="5" animated />
            </div>

            <div v-else-if="functionDetail.length > 0" class="function-content">
              <div v-for="(item, index) in functionDetail" :key="index" class="detail-item">
                <h4 class="detail-title">{{ item.title }}</h4>
                <div class="detail-content" v-html="item.content"></div>
              </div>
            </div>

            <el-empty v-else description="暂无功能点详情" />
          </el-card>
        </div>

        <!-- 右侧：测试用例表格 -->
        <div class="right-panel">
          <el-card class="test-case-card">
            <template #header>
              <div class="card-header">
                <span>关联测试用例</span>
                <el-tag type="info" size="small">共 {{ testCaseList.length }} 个用例</el-tag>
              </div>
            </template>

            <div v-if="caseLoading" class="loading-container">
              <el-skeleton :rows="8" animated />
            </div>

            <el-table v-else-if="testCaseList.length > 0" :data="testCaseList" style="width: 100%" border height="600">
              <el-table-column prop="case_id" label="用例ID" width="120" fixed="left" />
              <el-table-column prop="case_name" label="用例名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="priority" label="优先级" width="100">
                <template #default="{ row }">
                  <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="test_type" label="测试类型" width="120" />
              <el-table-column prop="pre_condition" label="前置条件" min-width="200" show-overflow-tooltip />
              <el-table-column label="锁定状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.isLocked ? 'success' : 'danger'">
                    {{ row.isLocked ? "已锁定" : "未锁定" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="评审状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getReviewStatusType(row.isReviewed)">
                    {{ getReviewStatusText(row.isReviewed) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleReview(row)">评审</el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-empty v-else description="暂无测试用例" />
          </el-card>
        </div>
      </div>
    </div>

    <!-- 用例评审弹窗 -->
    <el-dialog v-model="dialogVisible" :title="`评审测试用例 - ${currentCase?.case_name}`" width="60%" destroy-on-close>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="用例ID">{{ currentCase?.case_id }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityType(currentCase?.priority)">{{ currentCase?.priority }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="测试类型">{{ currentCase?.test_type }}</el-descriptions-item>
        <el-descriptions-item label="前置条件">{{ currentCase?.pre_condition }}</el-descriptions-item>
        <el-descriptions-item label="测试步骤">
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in currentCase?.steps"
              :key="index"
              :timestamp="`步骤 ${index + 1}`"
              placement="top"
            >
              {{ step }}
            </el-timeline-item>
          </el-timeline>
        </el-descriptions-item>
        <el-descriptions-item label="预期结果">{{ currentCase?.expected_result }}</el-descriptions-item>
        <el-descriptions-item label="当前评审状态">
          <el-tag :type="getReviewStatusType(currentCase?.isReviewed)">
            {{ getReviewStatusText(currentCase?.isReviewed) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <template #footer>
        <div class="dialog-footer">
          <!-- 如果当前状态为0（未评审），显示评审通过按钮 -->
          <el-button v-if="currentCase?.isReviewed === 0" type="success" @click="handleReviewAction(1)" :loading="reviewLoading">
            评审通过
          </el-button>
          <!-- 如果当前状态为1（已通过），显示评审不通过按钮 -->
          <el-button v-if="currentCase?.isReviewed === 1" type="danger" @click="handleReviewAction(0)" :loading="reviewLoading">
            评审不通过
          </el-button>
          <!-- 下一条按钮 -->
          <el-button
            v-if="currentCase?.isReviewed === 0"
            type="success"
            @click="handleReviewAndNext(1)"
            :loading="reviewLoading"
            plain
          >
            评审通过-下一条
          </el-button>
          <el-button
            v-if="currentCase?.isReviewed === 1"
            type="danger"
            @click="handleReviewAndNext(0)"
            :loading="reviewLoading"
            plain
          >
            评审不通过-下一条
          </el-button>
          <!-- 新增：仅跳转到下一条的按钮 -->
          <el-button type="info" @click="handleNextCase" plain>下一条</el-button>
          <!-- 关闭按钮移到最右侧 -->
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { TestCaseDetail, FunctionDetailItem, TreeNode } from "@/api/modules/service-llm/demand-doc";
import {
  api_getFunctionDetailById,
  api_getTestCaseByFunctionId,
  api_changeTestCaseReview
} from "@/api/modules/service-llm/demand-doc";

interface TestCaseDetailTable extends TestCaseDetail {
  id: string;
  functionId: string;
  isLocked: number;
  isReviewed: number; // 1表示评审通过，0表示未评审或评审不通过
}

const props = defineProps<{
  selectedNode: TreeNode | null;
}>();

const emit = defineEmits(["switchComponent"]);

const functionDetail = ref<FunctionDetailItem[]>([]);
const testCaseList = ref<TestCaseDetailTable[]>([]);
const loading = ref(false);
const caseLoading = ref(false);
const dialogVisible = ref(false);
const currentCase = ref<TestCaseDetailTable | null>(null);
const reviewLoading = ref(false);

const switchComponent = () => {
  emit("switchComponent", "TestCaseList");
};

const getPriorityType = (priority: string | undefined): "danger" | "warning" | "info" | undefined => {
  if (!priority) return undefined;
  if (priority === "高") {
    return "danger";
  }
  if (priority === "中") {
    return "warning";
  }
  if (priority === "低") {
    return "info";
  }
};

// 获取评审状态的标签类型
const getReviewStatusType = (isReviewed: number | undefined): "success" | "info" | undefined => {
  if (isReviewed === 1) {
    return "success";
  }
  return "info";
};

// 获取评审状态的文本
const getReviewStatusText = (isReviewed: number | undefined): string => {
  if (isReviewed === 1) {
    return "已通过";
  }
  return "未评审";
};

// 处理评审按钮点击
const handleReview = (row: TestCaseDetailTable) => {
  currentCase.value = row;
  dialogVisible.value = true;
};

// 处理评审操作
const handleReviewAction = (reviewStatus: number) => {
  if (!currentCase.value) {
    ElMessage.error("未找到要评审的测试用例");
    return;
  }

  reviewLoading.value = true;
  api_changeTestCaseReview(currentCase.value.id)
    .then(res => {
      if (res.respCode === 2000) {
        // 更新当前用例的评审状态
        currentCase.value!.isReviewed = reviewStatus;

        // 更新列表中对应用例的状态
        const index = testCaseList.value.findIndex(item => item.id === currentCase.value!.id);
        if (index !== -1) {
          testCaseList.value[index].isReviewed = reviewStatus;
        }

        ElMessage.success(`评审${reviewStatus === 1 ? "通过" : "不通过"}操作成功`);
        // 移除自动关闭弹窗的逻辑
        // dialogVisible.value = false;

        // 刷新测试用例列表以确保数据同步
        fetchTestCases();
      } else {
        ElMessage.error(`评审操作失败: ${res.respMsg || "未知错误"}`);
      }
    })
    .finally(() => {
      reviewLoading.value = false;
    });
};

// 处理评审并跳转到下一条
const handleReviewAndNext = function (reviewStatus: number) {
  if (!currentCase.value) {
    ElMessage.error("未找到要评审的测试用例");
    return;
  }

  reviewLoading.value = true;
  // 先执行评审操作
  api_changeTestCaseReview(currentCase.value.id)
    .then(async res => {
      if (res.respCode === 2000) {
        // 更新当前用例的评审状态
        currentCase.value!.isReviewed = reviewStatus;

        // 更新列表中对应用例的状态
        const currentIndex = testCaseList.value.findIndex(item => item.id === currentCase.value!.id);
        if (currentIndex !== -1) {
          testCaseList.value[currentIndex].isReviewed = reviewStatus;
        }

        ElMessage.success(`评审${reviewStatus === 1 ? "通过" : "不通过"}操作成功`);

        // 先刷新测试用例列表以确保数据同步
        await fetchTestCases();

        // 查找下一条用例（使用刷新后的列表）
        const nextIndex = currentIndex + 1;
        if (nextIndex < testCaseList.value.length) {
          // 切换到下一条用例
          currentCase.value = testCaseList.value[nextIndex];
        } else {
          // 已是最后一条用例
          ElMessage.warning("已是最后一条用例，评审完成");
          dialogVisible.value = false;
        }
      }
    })
    .finally(() => {
      reviewLoading.value = false;
    });
};

// 处理仅跳转到下一条用例（不改变评审状态）
const handleNextCase = function () {
  if (!currentCase.value) {
    ElMessage.error("未找到当前测试用例");
    return;
  }

  // 查找当前用例在列表中的索引
  const currentIndex = testCaseList.value.findIndex(item => item.id === currentCase.value!.id);
  
  // 查找下一条用例
  const nextIndex = currentIndex + 1;
  if (nextIndex < testCaseList.value.length) {
    // 切换到下一条用例
    currentCase.value = testCaseList.value[nextIndex];
  } else {
    // 已是最后一条用例
    ElMessage.warning("已是最后一条用例");
    dialogVisible.value = false;
  }
};

// 获取功能点详情
const fetchFunctionDetail = function () {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法获取功能点详情：未找到功能点ID");
    return;
  }

  loading.value = true;
  api_getFunctionDetailById(props.selectedNode.function_id)
    .then(res => {
      if (res.respCode === 2000 && res.respData) {
        functionDetail.value = res.respData.content;
        console.log(functionDetail.value);
      } else {
        ElMessage.error(res.respMsg);
        functionDetail.value = [];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 获取测试用例列表
const fetchTestCases = async () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法获取测试用例：未找到功能点ID");
    return;
  }

  caseLoading.value = true;
  await api_getTestCaseByFunctionId(props.selectedNode.function_id)
    .then(res => {
      if (res.respCode === 2000) {
        testCaseList.value = res.respData.data.map(item => {
          const statusMeta = JSON.parse(item.statusMeta as string);
          return {
            id: item.id,
            functionId: item.functionId,
            isLocked: item.isLocked,
            isReviewed: item.isReviewed || 0, // 添加评审状态字段
            case_id: statusMeta.case_id || "",
            case_name: statusMeta.case_name || "",
            priority: statusMeta.priority || "",
            test_type: statusMeta.test_type || "",
            pre_condition: statusMeta.pre_condition || "",
            steps: statusMeta.steps || [],
            expected_result: statusMeta.expected_result || "",
            actual_result: statusMeta.actual_result || ""
          } as TestCaseDetailTable;
        });
      } else {
        ElMessage.warning(res.respData?.status || "暂无测试用例");
        testCaseList.value = [];
      }
    })
    .finally(() => {
      caseLoading.value = false;
    });
};

// 监听selectedNode变化
watch(
  () => props.selectedNode,
  (newNode, oldNode) => {
    // 检查是否是真正的节点变更（避免相同节点的重复请求）
    if (newNode?.id !== oldNode?.id && newNode?.function_id) {
      fetchFunctionDetail();
      fetchTestCases();
    }
  }
);

onMounted(() => {
  if (props.selectedNode?.function_id) {
    fetchFunctionDetail();
    fetchTestCases();
  }
});
</script>

<style lang="scss" scoped>
.review-container {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
  height: 100%;
  position: relative;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    background-color: #fff;

    &.fixed {
      position: sticky;
      top: 0;
      z-index: 999;
      padding: 15px 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background: linear-gradient(to right, #ffffff, #f8f9fa);
    }

    .content-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #67c23a, #409eff);
        border-radius: 2px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      .el-button {
        padding: 8px 20px;
        font-weight: 500;
      }
    }
  }

  .content-body {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .review-layout {
      display: flex;
      gap: 20px;
      height: 100%;

      .left-panel {
        flex: 1;
        min-width: 400px;

        .function-detail-card {
          height: 100%;

          .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: 16px;
          }

          .loading-container {
            padding: 20px;
          }

          .function-content {
            max-height: 600px;
            overflow-y: auto;

            .detail-item {
              margin-bottom: 20px;
              padding-bottom: 15px;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .detail-title {
                color: #409eff;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 10px;
              }

              .detail-content {
                color: #606266;
                line-height: 1.6;
                white-space: pre-wrap;
              }
            }
          }
        }
      }

      .right-panel {
        flex: 2;
        min-width: 600px;

        .test-case-card {
          height: 100%;

          .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: 16px;
          }

          .loading-container {
            padding: 20px;
          }

          :deep(.el-table) {
            --el-table-border-color: #ebeef5;
            --el-table-header-bg-color: #f5f7fa;
          }
        }
      }
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  flex-wrap: wrap;

  .el-button {
    margin: 0;
  }
}
</style>
