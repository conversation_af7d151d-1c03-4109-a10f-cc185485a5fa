<template>
  <div class="resend-task-detail">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>重发任务详情 - {{ taskId }}</span>
          <el-button type="primary" size="small" @click="fetchData">刷新</el-button>
        </div>
      </template>

      <el-table :data="tableData" border v-loading="loading">
        <el-table-column prop="resendId" label="重发ID" />
        <el-table-column prop="taskId" label="任务ID" />
        <el-table-column prop="interfaceId" label="接口ID" />
        <!-- 可以根据实际返回的字段进行调整 -->
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { api_resendOverview } from '@/api/modules/service-enr/requestResend';
import type { EnrResendRecord } from '@/api/modules/service-enr/requestResend';

const props = defineProps<{
  taskId: string;
}>();

const loading = ref(false);
const tableData = ref<EnrResendRecord[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const fetchData = async () => {
  try {
    loading.value = true;
    const res = await api_resendOverview(
      { taskId: props.taskId } as any, 
      currentPage.value, 
      pageSize.value
    );
    
    if (res && res.respData) {
      tableData.value = res.respData.pageData || [];
      total.value = res.respData.totalCount || 0;
    }
  } catch (error) {
    console.error('获取重发任务详情失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  fetchData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchData();
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.resend-task-detail {
  padding: 20px;
}
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
